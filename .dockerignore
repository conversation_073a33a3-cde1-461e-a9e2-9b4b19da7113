# Git
.git
.gitignore
README.md
README_DEPLOYMENT.md

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
ENV/
env/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Database
*.db
*.sqlite
*.sqlite3

# Backups
backups/
*.backup
*.bak

# Temporary files
tmp/
temp/
.tmp/

# Documentation
docs/
*.md
!README.md

# Development files
.env.local
.env.development
.env.test
test_*
tests/

# Build artifacts
build/
dist/
*.egg-info/

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Deployment
deploy.sh
start_bot.sh
health_status.json

# Analysis files
*.json
*.txt
!requirements.txt
!insider_addresses.txt

# Monitoring
monitoring/
prometheus-data/
loki-data/
