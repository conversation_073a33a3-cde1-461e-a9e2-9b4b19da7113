# Solana Trading Bot Environment Variables

# Solana Configuration
SOLANA_PRIVATE_KEY=your_base58_encoded_private_key_here
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_WS_URL=wss://api.mainnet-beta.solana.com

# Database Configuration
DATABASE_URL=sqlite:///solana_trading_bot.db

# API Keys (Optional)
HELIUS_API_KEY=your_helius_api_key_here
QUICKNODE_API_KEY=your_quicknode_api_key_here
BIRDEYE_API_KEY=your_birdeye_api_key_here

# Trading Configuration
MAX_DAILY_LOSS_SOL=5.0
MAX_POSITION_SIZE_SOL=1.0
BASE_POSITION_SIZE_SOL=0.1

# Risk Management
STOP_LOSS_PERCENTAGE=0.15
TAKE_PROFIT_PERCENTAGE=2.0
SLIPPAGE_TOLERANCE=0.05

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_AUTHORIZED_USERS=123456789,987654321  # Comma-separated user IDs

# Feature Flags
ENABLE_COPY_TRADING=true
ENABLE_WALLET_DISCOVERY=true
ENABLE_PERFORMANCE_TRACKING=true
ENABLE_RISK_MANAGEMENT=true
ENABLE_TELEGRAM_BOT=true
ENABLE_NOTIFICATIONS=true

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/trading_bot.log

# =============================================================================
# REMOTE DEPLOYMENT CONFIGURATION
# =============================================================================

# Environment (development, staging, production)
ENVIRONMENT=production

# Enable test mode (uses smaller amounts)
TEST_MODE=false

# Devnet configuration (for testing)
USE_DEVNET=false
DEVNET_RPC_URL=https://api.devnet.solana.com

# =============================================================================
# PUMP.FUN CONFIGURATION
# =============================================================================

# Pump.fun WebSocket URL
PUMPFUN_WS_URL=wss://pumpportal.fun/api/data

# Monitoring intervals (seconds)
NEW_TOKEN_CHECK_INTERVAL=5
TRENDING_CHECK_INTERVAL=30

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Number of worker threads
WORKER_THREADS=4

# Connection pool size
CONNECTION_POOL_SIZE=10

# Request timeout (seconds)
REQUEST_TIMEOUT=30

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Encrypt sensitive data in database (true/false)
ENCRYPT_DATABASE=false

# API rate limiting
API_RATE_LIMIT_PER_MINUTE=60

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================

# Enable automatic backups
ENABLE_BACKUPS=true

# Backup interval (hours)
BACKUP_INTERVAL_HOURS=24

# Backup retention (days)
BACKUP_RETENTION_DAYS=30

# =============================================================================
# MONITORING & HEALTH CHECKS
# =============================================================================

# Health check interval (seconds)
HEALTH_CHECK_INTERVAL=60

# Enable metrics collection
ENABLE_METRICS=true

# Restart on consecutive failures
MAX_CONSECUTIVE_FAILURES=5
