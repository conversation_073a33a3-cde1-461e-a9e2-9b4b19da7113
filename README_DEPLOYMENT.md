# Solana Trading Bot - Remote Deployment Guide

This guide provides comprehensive instructions for deploying the Solana Trading Bot in a remote/production environment.

## 🚀 Quick Deployment

### Automated Deployment (Recommended)

```bash
# Clone the repository
git clone https://github.com/BigOSurfer/tradingbot.git
cd tradingbot

# Make deployment script executable
chmod +x deploy.sh

# Run automated deployment
./deploy.sh
```

### Manual Deployment

If you prefer manual setup or the automated script fails:

```bash
# 1. Install system dependencies
sudo apt update && sudo apt install -y python3 python3-pip git docker.io docker-compose

# 2. Clone repository
git clone https://github.com/BigOSurfer/tradingbot.git
cd tradingbot

# 3. Install Python dependencies
pip3 install -r requirements.txt

# 4. Configure environment
cp .env.example .env
# Edit .env with your configuration (see Configuration section)

# 5. Initialize database
python3 setup.py

# 6. Start the bot
./start_bot.sh start
```

## ⚙️ Configuration

### Required Environment Variables

Edit the `.env` file with your actual values:

```bash
# Essential Configuration
SOLANA_PRIVATE_KEY=your_actual_base58_private_key
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_AUTHORIZED_USERS=your_telegram_user_id
```

### Optional but Recommended

```bash
# Enhanced RPC Performance
HELIUS_API_KEY=your_helius_api_key

# Token Data APIs
BIRDEYE_API_KEY=your_birdeye_api_key
DEXSCREENER_API_KEY=your_dexscreener_api_key

# Trading Limits
MAX_DAILY_LOSS_SOL=5.0
MAX_POSITION_SIZE_SOL=1.0
```

## 🐳 Docker Deployment

### Using Docker Compose (Recommended)

```bash
# Build and start services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Using Docker Only

```bash
# Build image
docker build -t solana-trading-bot .

# Run container
docker run -d \
  --name solana-trading-bot \
  --env-file .env \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/data:/app/data \
  --restart unless-stopped \
  solana-trading-bot
```

## 🔧 Production Management

### Using the Start Script

```bash
# Make script executable
chmod +x start_bot.sh

# Start bot
./start_bot.sh start

# Check status
./start_bot.sh status

# View logs
./start_bot.sh logs

# Restart bot
./start_bot.sh restart

# Stop bot
./start_bot.sh stop

# Start with monitoring (auto-restart)
./start_bot.sh monitor
```

### Using Systemd Service

The deployment script automatically creates a systemd service:

```bash
# Start service
sudo systemctl start solana-trading-bot

# Enable auto-start on boot
sudo systemctl enable solana-trading-bot

# Check status
sudo systemctl status solana-trading-bot

# View logs
sudo journalctl -u solana-trading-bot -f
```

## 📊 Monitoring & Health Checks

### Health Check Script

```bash
# Run health check
python3 health_check.py

# Check specific components
python3 health_check.py --component database
python3 health_check.py --component rpc
```

### Log Monitoring

```bash
# Real-time logs
tail -f logs/trading_bot.log

# Search for errors
grep -i error logs/trading_bot.log

# View recent activity
./start_bot.sh logs 100
```

### Performance Monitoring

The bot includes built-in metrics collection:

- Database performance
- RPC response times
- Trading success rates
- Memory and CPU usage

## 🔒 Security Best Practices

### Private Key Security

1. **Never commit private keys to version control**
2. **Use environment variables only**
3. **Set proper file permissions**:
   ```bash
   chmod 600 .env
   ```
4. **Consider using hardware wallets for large amounts**

### System Security

```bash
# Create dedicated user
sudo useradd -m -s /bin/bash tradingbot
sudo usermod -aG docker tradingbot

# Set proper ownership
sudo chown -R tradingbot:tradingbot /opt/solana-trading-bot

# Run as non-root user
sudo -u tradingbot ./start_bot.sh start
```

### Network Security

- Use firewall to restrict access
- Consider VPN for remote access
- Monitor network connections

## 🔄 Backup & Recovery

### Automated Backups

The deployment script sets up automatic daily backups:

```bash
# Manual backup
/usr/local/bin/backup-trading-bot.sh

# Restore from backup
cd /opt/solana-trading-bot
tar -xzf /opt/backups/solana-trading-bot/backup_YYYYMMDD_HHMMSS.tar.gz
```

### Database Backup

```bash
# Backup database
cp solana_trading_bot.db backups/db_backup_$(date +%Y%m%d_%H%M%S).db

# Restore database
cp backups/db_backup_YYYYMMDD_HHMMSS.db solana_trading_bot.db
```

## 🚨 Troubleshooting

### Common Issues

1. **Bot won't start**
   ```bash
   # Check health
   python3 health_check.py
   
   # Check logs
   ./start_bot.sh logs
   
   # Verify configuration
   grep -v "^#" .env | grep -v "^$"
   ```

2. **RPC connection issues**
   ```bash
   # Test RPC connectivity
   curl -X POST -H "Content-Type: application/json" \
     -d '{"jsonrpc":"2.0","id":1,"method":"getHealth"}' \
     $SOLANA_RPC_URL
   ```

3. **Database issues**
   ```bash
   # Reinitialize database
   rm solana_trading_bot.db
   python3 setup.py
   ```

4. **Permission issues**
   ```bash
   # Fix permissions
   chmod +x *.sh
   chmod 600 .env
   chown -R $USER:$USER .
   ```

### Log Analysis

```bash
# Check for errors
grep -i "error\|exception\|failed" logs/trading_bot.log

# Monitor performance
grep -i "performance\|latency\|timeout" logs/trading_bot.log

# Track trades
grep -i "trade\|buy\|sell" logs/trading_bot.log
```

## 📈 Performance Optimization

### System Resources

- **Minimum**: 1 CPU, 2GB RAM, 10GB storage
- **Recommended**: 2 CPU, 4GB RAM, 50GB storage
- **High-frequency**: 4+ CPU, 8GB+ RAM, SSD storage

### Configuration Tuning

```bash
# Optimize for high-frequency trading
WORKER_THREADS=8
CONNECTION_POOL_SIZE=20
REQUEST_TIMEOUT=10

# Optimize for stability
WORKER_THREADS=2
CONNECTION_POOL_SIZE=5
REQUEST_TIMEOUT=30
```

## 🔄 Updates & Maintenance

### Updating the Bot

```bash
# Stop bot
./start_bot.sh stop

# Backup current version
/usr/local/bin/backup-trading-bot.sh

# Update code
git pull origin main

# Update dependencies
pip3 install -r requirements.txt

# Restart bot
./start_bot.sh start
```

### Regular Maintenance

1. **Daily**: Check logs and performance
2. **Weekly**: Review trading performance
3. **Monthly**: Update dependencies and system packages
4. **Quarterly**: Security audit and backup verification

## 📞 Support

### Getting Help

1. **Check logs**: `./start_bot.sh logs`
2. **Run health check**: `python3 health_check.py`
3. **Review configuration**: Ensure all required variables are set
4. **Check GitHub issues**: Look for similar problems
5. **Create new issue**: Provide logs and configuration details

### Useful Commands

```bash
# Quick status check
./start_bot.sh status && python3 health_check.py

# Full diagnostic
echo "=== System Info ===" && uname -a && \
echo "=== Disk Space ===" && df -h && \
echo "=== Memory ===" && free -h && \
echo "=== Bot Status ===" && ./start_bot.sh status

# Emergency stop
./start_bot.sh stop && docker-compose down
```

## ⚠️ Important Notes

1. **Test thoroughly** on devnet before mainnet deployment
2. **Start with small position sizes** to minimize risk
3. **Monitor closely** during initial deployment
4. **Keep backups** of configuration and database
5. **Never invest more than you can afford to lose**

## 📄 License

This deployment guide is part of the Solana Trading Bot project and follows the same MIT license terms.
