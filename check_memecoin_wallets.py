#!/usr/bin/env python3
"""
Quick verification of memecoin insider wallet addresses.
"""

import requests
import json

def check_wallet_exists(address):
    """Check if a wallet address exists on Solana blockchain."""
    try:
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getAccountInfo",
            "params": [
                address,
                {
                    "encoding": "base64",
                    "commitment": "confirmed"
                }
            ]
        }
        
        response = requests.post(
            "https://api.mainnet-beta.solana.com",
            json=payload,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            result = data.get('result')
            
            if result and result.get('value'):
                balance = result['value'].get('lamports', 0) / 1e9
                return True, balance
            else:
                return False, 0
        else:
            return False, 0
            
    except Exception as e:
        print(f"Error checking {address}: {e}")
        return False, 0

def main():
    """Check the top 5 memecoin insider wallets."""
    print("🔍 VERIFYING MEMECOIN INSIDER WALLETS")
    print("=" * 50)
    
    # Top 5 wallets from the memecoin analysis
    wallets_to_check = [
        "wsTJgQvNkNTqqjpdL1BXP4wZBxZCHeFDXw8RB8ASQqKr",
        "2Nu6Dmetbdxua2AjDo94vL6SHsWzjPKHjEmAv44WNUgc", 
        "YvQX119CSdmw5QYSr5M77a3P3xPTQ9UcwFSZhFX7X3yt",
        "xc5cc7CndMJ7wWdpqgiQtD4iLtnaqAqd9NXStMyPGNa1",
        "tqtbjSJcC3s8mGyYJiBVNSwsEZ5Y27KacqPBvLNimEQr"
    ]
    
    print(f"Checking {len(wallets_to_check)} wallet addresses...\n")
    
    real_wallets = 0
    fake_wallets = 0
    
    for i, wallet in enumerate(wallets_to_check, 1):
        print(f"Checking {i}/{len(wallets_to_check)}: {wallet[:8]}...{wallet[-8:]}")
        
        exists, balance = check_wallet_exists(wallet)
        
        if exists:
            print(f"   ✅ REAL - Balance: {balance:.4f} SOL")
            print(f"   🔗 https://solscan.io/account/{wallet}")
            real_wallets += 1
        else:
            print(f"   ❌ FAKE - Wallet does not exist")
            fake_wallets += 1
        
        print()
    
    print("=" * 50)
    print("📊 VERIFICATION SUMMARY:")
    print(f"✅ Real wallets: {real_wallets}")
    print(f"❌ Fake wallets: {fake_wallets}")
    
    if fake_wallets > 0:
        print(f"\n⚠️ WARNING: {fake_wallets} out of {len(wallets_to_check)} wallets are FAKE!")
        print("💡 The memecoin insider bot used simulated data for demonstration.")
        print("🔧 To get real wallets, connect to actual Solana transaction data.")

if __name__ == "__main__":
    main()
