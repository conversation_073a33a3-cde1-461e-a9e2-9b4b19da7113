#!/bin/bash

# Solana Trading Bot Deployment Script
# This script automates the deployment process for the trading bot

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
REPO_URL="https://github.com/BigOSurfer/tradingbot.git"
APP_DIR="/opt/solana-trading-bot"
SERVICE_NAME="solana-trading-bot"
BACKUP_DIR="/opt/backups/solana-trading-bot"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking system requirements..."
    
    # Check if running as root
    if [[ $EUID -eq 0 ]]; then
        log_error "This script should not be run as root for security reasons"
        exit 1
    fi
    
    # Check for required commands
    local required_commands=("git" "docker" "docker-compose" "python3" "pip3")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            log_error "$cmd is required but not installed"
            exit 1
        fi
    done
    
    log_success "All requirements met"
}

create_directories() {
    log_info "Creating necessary directories..."
    
    sudo mkdir -p "$APP_DIR"
    sudo mkdir -p "$BACKUP_DIR"
    sudo mkdir -p "/var/log/solana-trading-bot"
    
    # Set ownership
    sudo chown -R $USER:$USER "$APP_DIR"
    sudo chown -R $USER:$USER "$BACKUP_DIR"
    
    log_success "Directories created"
}

clone_or_update_repo() {
    log_info "Setting up application code..."
    
    if [ -d "$APP_DIR/.git" ]; then
        log_info "Updating existing repository..."
        cd "$APP_DIR"
        git fetch origin
        git reset --hard origin/main
    else
        log_info "Cloning repository..."
        git clone "$REPO_URL" "$APP_DIR"
        cd "$APP_DIR"
    fi
    
    log_success "Repository setup complete"
}

setup_environment() {
    log_info "Setting up environment..."
    
    cd "$APP_DIR"
    
    # Copy environment file if it doesn't exist
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            log_warning "Created .env from template. Please edit with your configuration!"
        else
            log_error ".env.example not found"
            exit 1
        fi
    fi
    
    # Install Python dependencies
    python3 -m pip install --user -r requirements.txt
    
    log_success "Environment setup complete"
}

setup_database() {
    log_info "Setting up database..."
    
    cd "$APP_DIR"
    python3 setup.py
    
    log_success "Database setup complete"
}

build_docker_image() {
    log_info "Building Docker image..."
    
    cd "$APP_DIR"
    docker-compose build --no-cache
    
    log_success "Docker image built"
}

create_systemd_service() {
    log_info "Creating systemd service..."
    
    sudo tee /etc/systemd/system/${SERVICE_NAME}.service > /dev/null <<EOF
[Unit]
Description=Solana Trading Bot
After=network.target docker.service
Requires=docker.service

[Service]
Type=forking
User=$USER
Group=$USER
WorkingDirectory=$APP_DIR
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
ExecReload=/usr/local/bin/docker-compose restart
Restart=always
RestartSec=30

[Install]
WantedBy=multi-user.target
EOF

    sudo systemctl daemon-reload
    sudo systemctl enable ${SERVICE_NAME}
    
    log_success "Systemd service created"
}

setup_monitoring() {
    log_info "Setting up monitoring..."
    
    cd "$APP_DIR"
    
    # Create monitoring directory
    mkdir -p monitoring
    
    # Create basic Prometheus config
    cat > monitoring/prometheus.yml <<EOF
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'trading-bot'
    static_configs:
      - targets: ['solana-trading-bot:8080']
EOF
    
    log_success "Monitoring setup complete"
}

create_backup_script() {
    log_info "Creating backup script..."
    
    sudo tee /usr/local/bin/backup-trading-bot.sh > /dev/null <<EOF
#!/bin/bash
TIMESTAMP=\$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/backup_\$TIMESTAMP.tar.gz"

cd "$APP_DIR"
tar -czf "\$BACKUP_FILE" \
    --exclude='.git' \
    --exclude='__pycache__' \
    --exclude='*.pyc' \
    .

# Keep only last 30 backups
find "$BACKUP_DIR" -name "backup_*.tar.gz" -type f -mtime +30 -delete

echo "Backup created: \$BACKUP_FILE"
EOF

    sudo chmod +x /usr/local/bin/backup-trading-bot.sh
    
    # Add to crontab for daily backups
    (crontab -l 2>/dev/null; echo "0 2 * * * /usr/local/bin/backup-trading-bot.sh") | crontab -
    
    log_success "Backup script created"
}

start_services() {
    log_info "Starting services..."
    
    cd "$APP_DIR"
    
    # Start the main service
    sudo systemctl start ${SERVICE_NAME}
    
    # Check status
    sleep 10
    if sudo systemctl is-active --quiet ${SERVICE_NAME}; then
        log_success "Service started successfully"
    else
        log_error "Service failed to start"
        sudo systemctl status ${SERVICE_NAME}
        exit 1
    fi
}

show_status() {
    log_info "Deployment Status:"
    echo "===================="
    
    cd "$APP_DIR"
    
    echo "Service Status:"
    sudo systemctl status ${SERVICE_NAME} --no-pager -l
    
    echo -e "\nDocker Status:"
    docker-compose ps
    
    echo -e "\nLogs (last 20 lines):"
    docker-compose logs --tail=20
    
    echo -e "\nNext Steps:"
    echo "1. Edit $APP_DIR/.env with your configuration"
    echo "2. Restart the service: sudo systemctl restart ${SERVICE_NAME}"
    echo "3. Monitor logs: docker-compose logs -f"
    echo "4. Check health: python3 health_check.py"
}

# Main deployment process
main() {
    log_info "Starting Solana Trading Bot deployment..."
    
    check_requirements
    create_directories
    clone_or_update_repo
    setup_environment
    setup_database
    build_docker_image
    create_systemd_service
    setup_monitoring
    create_backup_script
    start_services
    show_status
    
    log_success "Deployment completed successfully!"
    log_warning "Don't forget to configure your .env file with actual values!"
}

# Run main function
main "$@"
