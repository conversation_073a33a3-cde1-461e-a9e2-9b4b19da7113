version: '3.8'

services:
  solana-trading-bot:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: solana-trading-bot
    restart: unless-stopped
    
    # Environment variables from .env file
    env_file:
      - .env
    
    # Volume mounts for persistent data
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./backups:/app/backups
      - ./solana_trading_bot.db:/app/solana_trading_bot.db
    
    # Network configuration
    networks:
      - trading-bot-network
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    
    # Health check
    healthcheck:
      test: ["CMD", "python", "health_check.py"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 30s
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"
    
    # Security options
    security_opt:
      - no-new-privileges:true
    
    # Read-only root filesystem (except for mounted volumes)
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=100m

  # Optional: Monitoring service
  monitoring:
    image: prom/prometheus:latest
    container_name: trading-bot-monitoring
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - trading-bot-network
    profiles:
      - monitoring

  # Optional: Log aggregation
  log-aggregator:
    image: grafana/loki:latest
    container_name: trading-bot-logs
    restart: unless-stopped
    ports:
      - "3100:3100"
    volumes:
      - loki-data:/loki
    networks:
      - trading-bot-network
    profiles:
      - monitoring

networks:
  trading-bot-network:
    driver: bridge

volumes:
  prometheus-data:
  loki-data:
