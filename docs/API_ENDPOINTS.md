# API Endpoints Documentation

This document contains the accurate API URLs for the Solana Trading Bot project, retrieved from official documentation.

## 🚀 Jupiter API (Swap Aggregator)

### New Endpoints (Current - March 2025)
**Important**: Jupiter migrated to new hostnames. Old URLs will be phased out by May 1, 2025.

#### Free Tier (No API Key Required)
- **Quote**: `https://lite-api.jup.ag/swap/v1/quote`
- **Swap**: `https://lite-api.jup.ag/swap/v1/swap`
- **Swap Instructions**: `https://lite-api.jup.ag/swap/v1/swap-instructions`
- **Price**: `https://lite-api.jup.ag/price/v2`
- **Tokens**: `https://lite-api.jup.ag/tokens/v1/mints/tradable`
- **Token Info**: `https://lite-api.jup.ag/tokens/v1/token/{mint}`

#### Paid Tier (API Key Required)
- **Quote**: `https://api.jup.ag/swap/v1/quote`
- **Swap**: `https://api.jup.ag/swap/v1/swap`
- **Price**: `https://api.jup.ag/price/v2`

#### Trigger API (Limit Orders)
- **Create Order**: `https://lite-api.jup.ag/trigger/v1/createOrder`
- **Execute Order**: `https://lite-api.jup.ag/trigger/v1/execute`
- **Cancel Order**: `https://lite-api.jup.ag/trigger/v1/cancelOrder`
- **Get Orders**: `https://lite-api.jup.ag/trigger/v1/getTriggerOrders`

#### Legacy Endpoints (Being Phased Out)
- **Quote**: `https://quote-api.jup.ag/v6/quote`
- **Swap**: `https://quote-api.jup.ag/v6/swap`
- **Price**: `https://price.jup.ag/v6`

**Rate Limits**: 60 RPM (free), 600 RPM (paid)

## 🐦 Birdeye API (Market Data)

### Base URL
`https://public-api.birdeye.so`

### Endpoints
- **Price**: `/defi/price?address={token_address}&chain=solana`
- **Multi Price**: `/defi/multi_price`
- **Token Overview**: `/defi/token_overview?address={token_address}`
- **Token Security**: `/defi/token_security?address={token_address}`
- **Trades (Token)**: `/defi/txs/token?address={token_address}`
- **Trades (Pair)**: `/defi/txs/pair?address={pair_address}`
- **OHLCV**: `/defi/ohlcv?address={token_address}&type={timeframe}`
- **New Listings**: `/defi/v2/tokens/new_listing?chain=solana`
- **Top Traders**: `/defi/v2/tokens/top_traders?address={token_address}`
- **Trending**: `/defi/token_trending?chain=solana`
- **Search**: `/defi/v3/search?keyword={query}`

### WebSocket
- **URL**: `wss://public-api.birdeye.so/socket/solana?x-api-key={api_key}`

**Rate Limits**: 100 RPM (starter), 300 RPM (standard), 1000 RPM (premium)

## 📊 DexScreener API (Token Data)

### Base URL
`https://api.dexscreener.com`

### Endpoints
- **Token Pairs**: `/latest/dex/pairs/{chainId}/{pairId}`
- **Search**: `/latest/dex/search?q={query}`
- **Token Info**: `/tokens/v1/{chainId}/{tokenAddresses}`
- **Token Pairs by Address**: `/token-pairs/v1/{chainId}/{tokenAddress}`
- **Token Profiles**: `/token-profiles/latest/v1`
- **Token Boosts**: `/token-boosts/latest/v1`

**Rate Limits**: 60 RPM (profiles), 300 RPM (pairs/tokens)

## 🚀 Pump.fun API (New Token Launches)

### API Versions
- **V1 (Deprecated)**: `https://frontend-api.pump.fun`
- **V2 (Current)**: `https://frontend-api-v2.pump.fun`
- **V3 (Latest)**: `https://frontend-api-v3.pump.fun`

### V3 Endpoints (Recommended)
- **Coins**: `/coins`
- **Latest Coins**: `/coins/latest`
- **Live Coins**: `/coins/currently-live`
- **Featured Coins**: `/coins/featured/{timeWindow}`
- **Search Coins**: `/coins/search?q={query}`
- **Coin Details**: `/coins/{mint}`
- **Latest Trades**: `/trades/latest`
- **All Trades**: `/trades/all/{mint}`
- **SOL Price**: `/sol-price`
- **Candlesticks**: `/candlesticks/{mint}`

**Rate Limits**: ~60 RPM (estimated)

## ⚡ Helius RPC (Solana Blockchain)

### Standard RPC
- **Mainnet**: `https://mainnet.helius-rpc.com/?api-key={api_key}`
- **Devnet**: `https://devnet.helius-rpc.com/?api-key={api_key}`

### Staked Connections (Priority)
- **Mainnet**: `https://staked.helius-rpc.com?api-key={api_key}`
- **Cost**: 50 credits per request

### WebSocket
- **Standard**: `wss://mainnet.helius-rpc.com/?api-key={api_key}`
- **Enhanced**: `wss://atlas-mainnet.helius-rpc.com/?api-key={api_key}`

**Rate Limits**: 100 RPM (free), 1000 RPM (developer), 10000 RPM (professional)

## 🔧 QuickNode Pump.fun API (Alternative)

### Endpoints
- **Quote**: `https://docs-demo.solana-mainnet.quiknode.pro/pump-fun/quote`
- **Swap**: `https://docs-demo.solana-mainnet.quiknode.pro/pump-fun/swap`

**Rate Limits**: 300 RPM

## 🔑 API Keys Configuration

Add these to your `.env` file:

```env
# Jupiter API (optional for free tier)
JUPITER_API_KEY=your_jupiter_api_key

# Birdeye API (required)
BIRDEYE_API_KEY=your_birdeye_api_key

# Helius RPC (recommended)
HELIUS_API_KEY=your_helius_api_key

# QuickNode (optional)
QUICKNODE_API_KEY=your_quicknode_api_key
```

## 📝 Usage Examples

### Jupiter Quote
```python
import requests

url = "https://lite-api.jup.ag/swap/v1/quote"
params = {
    "inputMint": "So11111111111111111111111111111111111111112",  # SOL
    "outputMint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
    "amount": "100000000",  # 0.1 SOL
    "slippageBps": "50"
}
response = requests.get(url, params=params)
```

### Birdeye Price
```python
import requests

url = "https://public-api.birdeye.so/defi/price"
params = {
    "address": "So11111111111111111111111111111111111111112",
    "chain": "solana"
}
headers = {"X-API-KEY": "your_api_key"}
response = requests.get(url, params=params, headers=headers)
```

### Pump.fun Latest Coins
```python
import requests

url = "https://frontend-api-v3.pump.fun/coins/latest"
response = requests.get(url)
```

## ⚠️ Important Notes

1. **Jupiter Migration**: Old URLs (`quote-api.jup.ag`) will stop working May 1, 2025
2. **Rate Limiting**: All APIs have rate limits - implement proper throttling
3. **API Keys**: Some endpoints require API keys for higher limits
4. **Error Handling**: Always implement retry logic with exponential backoff
5. **WebSocket Connections**: Implement ping/pong to maintain connections

## 🔄 Migration Checklist

- [ ] Update Jupiter URLs to new hostnames
- [ ] Test all API endpoints
- [ ] Implement rate limiting
- [ ] Add API key authentication
- [ ] Set up error handling and retries
- [ ] Monitor API usage and costs
