#!/usr/bin/env python3
"""
Health Check Script for Solana Trading Bot
Monitors the bot's health and provides status information
"""

import os
import sys
import time
import json
import sqlite3
import asyncio
import aiohttp
from datetime import datetime, timedelta
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

class HealthChecker:
    def __init__(self):
        self.checks = []
        self.status = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "unknown",
            "checks": {},
            "metrics": {}
        }
    
    def add_check(self, name, check_func, critical=True):
        """Add a health check"""
        self.checks.append({
            "name": name,
            "func": check_func,
            "critical": critical
        })
    
    async def run_checks(self):
        """Run all health checks"""
        print("🔍 Running health checks...")
        
        critical_failures = 0
        total_checks = len(self.checks)
        
        for check in self.checks:
            try:
                result = await check["func"]()
                self.status["checks"][check["name"]] = {
                    "status": "pass" if result["success"] else "fail",
                    "message": result.get("message", ""),
                    "data": result.get("data", {}),
                    "critical": check["critical"]
                }
                
                if not result["success"]:
                    if check["critical"]:
                        critical_failures += 1
                    print(f"❌ {check['name']}: {result.get('message', 'Failed')}")
                else:
                    print(f"✅ {check['name']}: {result.get('message', 'OK')}")
                    
            except Exception as e:
                self.status["checks"][check["name"]] = {
                    "status": "error",
                    "message": str(e),
                    "critical": check["critical"]
                }
                if check["critical"]:
                    critical_failures += 1
                print(f"💥 {check['name']}: Error - {str(e)}")
        
        # Determine overall status
        if critical_failures > 0:
            self.status["overall_status"] = "critical"
        elif any(c["status"] != "pass" for c in self.status["checks"].values()):
            self.status["overall_status"] = "warning"
        else:
            self.status["overall_status"] = "healthy"
        
        return self.status
    
    async def check_database_connection(self):
        """Check database connectivity"""
        try:
            db_path = "solana_trading_bot.db"
            if not os.path.exists(db_path):
                return {"success": False, "message": "Database file not found"}
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            conn.close()
            
            return {"success": True, "message": "Database accessible"}
        except Exception as e:
            return {"success": False, "message": f"Database error: {str(e)}"}
    
    async def check_environment_variables(self):
        """Check required environment variables"""
        required_vars = [
            "SOLANA_PRIVATE_KEY",
            "SOLANA_RPC_URL"
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var) or os.getenv(var).startswith("REPLACE_WITH"):
                missing_vars.append(var)
        
        if missing_vars:
            return {
                "success": False,
                "message": f"Missing or placeholder environment variables: {', '.join(missing_vars)}"
            }
        
        return {"success": True, "message": "All required environment variables set"}
    
    async def check_solana_rpc_connection(self):
        """Check Solana RPC connectivity"""
        try:
            rpc_url = os.getenv("SOLANA_RPC_URL", "https://api.mainnet-beta.solana.com")
            
            async with aiohttp.ClientSession() as session:
                payload = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "getHealth"
                }
                
                async with session.post(rpc_url, json=payload, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        if "result" in data:
                            return {"success": True, "message": "Solana RPC accessible"}
                    
                    return {"success": False, "message": f"RPC returned status {response.status}"}
        
        except Exception as e:
            return {"success": False, "message": f"RPC connection error: {str(e)}"}
    
    async def check_disk_space(self):
        """Check available disk space"""
        try:
            import shutil
            total, used, free = shutil.disk_usage(".")
            free_gb = free // (1024**3)
            
            if free_gb < 1:
                return {"success": False, "message": f"Low disk space: {free_gb}GB free"}
            
            return {
                "success": True,
                "message": f"Disk space OK: {free_gb}GB free",
                "data": {"free_gb": free_gb}
            }
        except Exception as e:
            return {"success": False, "message": f"Disk check error: {str(e)}"}
    
    async def check_log_files(self):
        """Check log file accessibility"""
        try:
            log_dir = Path("logs")
            if not log_dir.exists():
                log_dir.mkdir(exist_ok=True)
            
            log_file = log_dir / "trading_bot.log"
            
            # Try to write to log file
            with open(log_file, "a") as f:
                f.write(f"Health check: {datetime.now().isoformat()}\n")
            
            return {"success": True, "message": "Log files accessible"}
        except Exception as e:
            return {"success": False, "message": f"Log file error: {str(e)}"}
    
    async def check_recent_activity(self):
        """Check for recent bot activity"""
        try:
            log_file = Path("logs/trading_bot.log")
            if not log_file.exists():
                return {"success": False, "message": "No log file found"}
            
            # Check if log file has been modified in the last 5 minutes
            last_modified = datetime.fromtimestamp(log_file.stat().st_mtime)
            time_diff = datetime.now() - last_modified
            
            if time_diff > timedelta(minutes=5):
                return {
                    "success": False,
                    "message": f"No recent activity (last: {time_diff} ago)"
                }
            
            return {"success": True, "message": "Recent activity detected"}
        except Exception as e:
            return {"success": False, "message": f"Activity check error: {str(e)}"}

async def main():
    """Main health check function"""
    checker = HealthChecker()
    
    # Add health checks
    checker.add_check("database", checker.check_database_connection, critical=True)
    checker.add_check("environment", checker.check_environment_variables, critical=True)
    checker.add_check("solana_rpc", checker.check_solana_rpc_connection, critical=True)
    checker.add_check("disk_space", checker.check_disk_space, critical=False)
    checker.add_check("log_files", checker.check_log_files, critical=False)
    checker.add_check("recent_activity", checker.check_recent_activity, critical=False)
    
    # Run checks
    status = await checker.run_checks()
    
    # Print summary
    print(f"\n📊 Health Check Summary")
    print("=" * 50)
    print(f"Overall Status: {status['overall_status'].upper()}")
    print(f"Timestamp: {status['timestamp']}")
    print(f"Checks Passed: {sum(1 for c in status['checks'].values() if c['status'] == 'pass')}/{len(status['checks'])}")
    
    # Save status to file
    with open("health_status.json", "w") as f:
        json.dump(status, f, indent=2)
    
    # Exit with appropriate code
    if status["overall_status"] == "critical":
        sys.exit(1)
    elif status["overall_status"] == "warning":
        sys.exit(2)
    else:
        sys.exit(0)

if __name__ == "__main__":
    asyncio.run(main())
