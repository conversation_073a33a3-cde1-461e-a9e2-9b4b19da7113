#!/usr/bin/env python3
"""
Insider Wallet Manager - Manage and monitor identified insider wallets.
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import asyncio


@dataclass
class InsiderWallet:
    """Represents an insider wallet with performance metrics."""
    rank: int
    wallet_address: str
    total_pnl_sol: float
    total_roi_percentage: float
    win_rate_percent: float
    avg_entry_speed_minutes: float
    largest_win_sol: float
    total_trades: int
    insider_score: float
    status: str = "active"
    risk_level: str = "high_performer"
    notes: str = ""
    last_updated: str = ""


class InsiderWalletManager:
    """Manages insider wallet data and monitoring."""
    
    def __init__(self, wallet_file: str = "insider_wallets.json"):
        self.wallet_file = wallet_file
        self.wallets = {}
        self.metadata = {}
        self.load_wallets()
    
    def load_wallets(self):
        """Load wallets from JSON file."""
        try:
            if os.path.exists(self.wallet_file):
                with open(self.wallet_file, 'r') as f:
                    data = json.load(f)
                    self.metadata = data.get('analysis_metadata', {})
                    
                    # Load wallet data
                    for wallet_data in data.get('top_insider_wallets', []):
                        wallet = InsiderWallet(**wallet_data)
                        self.wallets[wallet.wallet_address] = wallet
                        
                print(f"✅ Loaded {len(self.wallets)} insider wallets from {self.wallet_file}")
            else:
                print(f"⚠️ Wallet file {self.wallet_file} not found. Starting with empty database.")
                
        except Exception as e:
            print(f"❌ Error loading wallets: {e}")
            self.wallets = {}
            self.metadata = {}
    
    def save_wallets(self):
        """Save wallets to JSON file."""
        try:
            # Prepare data structure
            wallet_list = []
            for wallet in sorted(self.wallets.values(), key=lambda x: x.insider_score, reverse=True):
                wallet.last_updated = datetime.now().isoformat()
                wallet_list.append(asdict(wallet))
            
            data = {
                "analysis_metadata": self.metadata,
                "top_insider_wallets": wallet_list,
                "monitoring_config": {
                    "auto_monitor": True,
                    "alert_on_new_trades": True,
                    "copy_trade_enabled": False,
                    "max_copy_amount_sol": 0.5,
                    "notification_threshold_sol": 1.0,
                    "update_frequency_minutes": 5
                },
                "risk_categories": {
                    "high_performer": {
                        "description": "Wallets with insider score > 45 and consistent profits",
                        "monitoring_priority": "high",
                        "copy_trade_consideration": "yes_with_limits"
                    },
                    "medium_performer": {
                        "description": "Wallets with insider score 30-45",
                        "monitoring_priority": "medium",
                        "copy_trade_consideration": "cautious"
                    },
                    "low_performer": {
                        "description": "Wallets with insider score < 30",
                        "monitoring_priority": "low",
                        "copy_trade_consideration": "no"
                    }
                },
                "last_updated": datetime.now().isoformat()
            }
            
            with open(self.wallet_file, 'w') as f:
                json.dump(data, f, indent=2)
                
            print(f"✅ Saved {len(self.wallets)} wallets to {self.wallet_file}")
            
        except Exception as e:
            print(f"❌ Error saving wallets: {e}")
    
    def add_wallet(self, wallet: InsiderWallet):
        """Add or update a wallet."""
        self.wallets[wallet.wallet_address] = wallet
        print(f"✅ Added/updated wallet: {wallet.wallet_address[:8]}...{wallet.wallet_address[-8:]}")
    
    def remove_wallet(self, wallet_address: str):
        """Remove a wallet."""
        if wallet_address in self.wallets:
            del self.wallets[wallet_address]
            print(f"✅ Removed wallet: {wallet_address[:8]}...{wallet_address[-8:]}")
        else:
            print(f"⚠️ Wallet not found: {wallet_address}")
    
    def get_wallet(self, wallet_address: str) -> Optional[InsiderWallet]:
        """Get a specific wallet."""
        return self.wallets.get(wallet_address)
    
    def get_top_wallets(self, limit: int = 10) -> List[InsiderWallet]:
        """Get top wallets by insider score."""
        return sorted(self.wallets.values(), key=lambda x: x.insider_score, reverse=True)[:limit]
    
    def get_wallets_by_risk_level(self, risk_level: str) -> List[InsiderWallet]:
        """Get wallets by risk level."""
        return [w for w in self.wallets.values() if w.risk_level == risk_level]
    
    def get_wallet_addresses(self) -> List[str]:
        """Get all wallet addresses."""
        return list(self.wallets.keys())
    
    def update_wallet_status(self, wallet_address: str, status: str):
        """Update wallet status."""
        if wallet_address in self.wallets:
            self.wallets[wallet_address].status = status
            print(f"✅ Updated wallet {wallet_address[:8]}... status to: {status}")
        else:
            print(f"⚠️ Wallet not found: {wallet_address}")
    
    def print_wallet_summary(self):
        """Print summary of all wallets."""
        print("\n" + "="*80)
        print("📊 INSIDER WALLET SUMMARY")
        print("="*80)
        
        if not self.wallets:
            print("❌ No wallets found")
            return
        
        print(f"📈 Total Wallets: {len(self.wallets)}")
        
        # Group by risk level
        risk_counts = {}
        total_pnl = 0
        for wallet in self.wallets.values():
            risk_counts[wallet.risk_level] = risk_counts.get(wallet.risk_level, 0) + 1
            total_pnl += wallet.total_pnl_sol
        
        print(f"💰 Total Combined P&L: {total_pnl:.2f} SOL")
        print(f"🎯 Risk Level Distribution:")
        for risk_level, count in risk_counts.items():
            print(f"   {risk_level}: {count} wallets")
        
        print(f"\n🏆 TOP 5 WALLETS:")
        for i, wallet in enumerate(self.get_top_wallets(5), 1):
            print(f"   #{i} {wallet.wallet_address[:8]}...{wallet.wallet_address[-8:]} - "
                  f"Score: {wallet.insider_score}, P&L: {wallet.total_pnl_sol:.2f} SOL")
    
    def export_addresses_only(self, filename: str = "insider_addresses.txt"):
        """Export just the wallet addresses to a text file."""
        try:
            with open(filename, 'w') as f:
                f.write("# Insider Wallet Addresses\n")
                f.write(f"# Generated: {datetime.now().isoformat()}\n")
                f.write(f"# Total Wallets: {len(self.wallets)}\n\n")
                
                for wallet in self.get_top_wallets():
                    f.write(f"{wallet.wallet_address}\n")
            
            print(f"✅ Exported {len(self.wallets)} addresses to {filename}")
            
        except Exception as e:
            print(f"❌ Error exporting addresses: {e}")


def main():
    """Main function for command-line usage."""
    manager = InsiderWalletManager()
    
    while True:
        print("\n" + "="*50)
        print("🎯 INSIDER WALLET MANAGER")
        print("="*50)
        print("1. View wallet summary")
        print("2. View top wallets")
        print("3. Search wallet")
        print("4. Update wallet status")
        print("5. Export addresses")
        print("6. Reload wallets")
        print("7. Save wallets")
        print("0. Exit")
        
        choice = input("\nEnter your choice: ").strip()
        
        if choice == "1":
            manager.print_wallet_summary()
            
        elif choice == "2":
            limit = input("How many top wallets to show? (default 10): ").strip()
            limit = int(limit) if limit.isdigit() else 10
            
            print(f"\n🏆 TOP {limit} INSIDER WALLETS:")
            for i, wallet in enumerate(manager.get_top_wallets(limit), 1):
                print(f"\n#{i} {wallet.wallet_address[:8]}...{wallet.wallet_address[-8:]}")
                print(f"   💰 P&L: {wallet.total_pnl_sol:.4f} SOL")
                print(f"   📈 ROI: {wallet.total_roi_percentage:.1f}%")
                print(f"   🎖️ Score: {wallet.insider_score}")
                print(f"   ⚡ Entry Speed: {wallet.avg_entry_speed_minutes:.1f} min")
                print(f"   📊 Status: {wallet.status}")
                
        elif choice == "3":
            address = input("Enter wallet address (or partial): ").strip()
            found = False
            for wallet_addr, wallet in manager.wallets.items():
                if address.lower() in wallet_addr.lower():
                    print(f"\n✅ Found: {wallet_addr}")
                    print(f"   💰 P&L: {wallet.total_pnl_sol:.4f} SOL")
                    print(f"   📈 ROI: {wallet.total_roi_percentage:.1f}%")
                    print(f"   🎖️ Score: {wallet.insider_score}")
                    print(f"   📊 Status: {wallet.status}")
                    found = True
            
            if not found:
                print("❌ No matching wallets found")
                
        elif choice == "4":
            address = input("Enter wallet address: ").strip()
            status = input("Enter new status (active/inactive/monitoring/flagged): ").strip()
            manager.update_wallet_status(address, status)
            
        elif choice == "5":
            filename = input("Enter filename (default: insider_addresses.txt): ").strip()
            if not filename:
                filename = "insider_addresses.txt"
            manager.export_addresses_only(filename)
            
        elif choice == "6":
            manager.load_wallets()
            
        elif choice == "7":
            manager.save_wallets()
            
        elif choice == "0":
            print("👋 Goodbye!")
            break
            
        else:
            print("❌ Invalid choice. Please try again.")


if __name__ == "__main__":
    main()
