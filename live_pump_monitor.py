#!/usr/bin/env python3
"""
Live Pump.fun Monitor - 1 Hour Session
Shows real-time pump.fun activity with enhanced formatting and statistics.
"""

import asyncio
import websockets
import json
import time
from datetime import datetime, timedelta
from collections import defaultdict
import signal
import sys


class LivePumpMonitor:
    """Live monitor for pump.fun activity with enhanced display."""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.session_duration = 3600  # 1 hour in seconds
        self.is_running = False
        
        # Statistics
        self.stats = {
            'new_tokens': 0,
            'total_trades': 0,
            'total_volume_sol': 0.0,
            'unique_traders': set(),
            'tokens_seen': set(),
            'large_trades': 0,  # > 5 SOL
            'whale_trades': 0,  # > 20 SOL
        }
        
        # Recent activity
        self.recent_tokens = []
        self.recent_trades = []
        self.top_tokens = defaultdict(lambda: {'volume': 0, 'trades': 0, 'name': 'Unknown'})
        
    def format_time_elapsed(self):
        """Format elapsed time since start."""
        elapsed = datetime.now() - self.start_time
        return str(elapsed).split('.')[0]  # Remove microseconds
    
    def format_time_remaining(self):
        """Format time remaining in session."""
        elapsed_seconds = (datetime.now() - self.start_time).total_seconds()
        remaining_seconds = max(0, self.session_duration - elapsed_seconds)
        remaining = timedelta(seconds=int(remaining_seconds))
        return str(remaining)
    
    def print_header(self):
        """Print session header."""
        print("\n" + "="*80)
        print("🚀 LIVE PUMP.FUN MONITOR - 1 HOUR SESSION")
        print("="*80)
        print(f"⏰ Session started: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 Duration: 1 hour")
        print(f"📡 Source: PumpPortal WebSocket (wss://pumpportal.fun/api/data)")
        print("="*80)
    
    def print_stats(self):
        """Print current session statistics."""
        elapsed = self.format_time_elapsed()
        remaining = self.format_time_remaining()
        
        print(f"\n📊 SESSION STATS [{elapsed} elapsed, {remaining} remaining]")
        print("-" * 60)
        print(f"🪙 New Tokens Detected: {self.stats['new_tokens']}")
        print(f"💰 Total Trades: {self.stats['total_trades']}")
        print(f"💎 Total Volume: {self.stats['total_volume_sol']:.2f} SOL")
        print(f"👥 Unique Traders: {len(self.stats['unique_traders'])}")
        print(f"🔥 Large Trades (>5 SOL): {self.stats['large_trades']}")
        print(f"🐋 Whale Trades (>20 SOL): {self.stats['whale_trades']}")
        
        if self.stats['total_trades'] > 0:
            avg_trade = self.stats['total_volume_sol'] / self.stats['total_trades']
            print(f"📈 Average Trade Size: {avg_trade:.4f} SOL")
    
    def print_recent_activity(self):
        """Print recent tokens and trades."""
        print(f"\n🆕 RECENT NEW TOKENS (Last 5):")
        print("-" * 40)
        for token in self.recent_tokens[-5:]:
            print(f"  🪙 {token['name']} ({token['symbol']}) - {token['time']}")
        
        print(f"\n💰 RECENT LARGE TRADES (>1 SOL, Last 5):")
        print("-" * 40)
        for trade in self.recent_trades[-5:]:
            action = "🟢 BUY" if trade['is_buy'] else "🔴 SELL"
            print(f"  {action} {trade['amount']:.2f} SOL of {trade['token']} - {trade['time']}")
    
    def print_top_tokens(self):
        """Print top tokens by volume."""
        if not self.top_tokens:
            return
            
        print(f"\n🏆 TOP TOKENS BY VOLUME:")
        print("-" * 50)
        sorted_tokens = sorted(
            self.top_tokens.items(), 
            key=lambda x: x[1]['volume'], 
            reverse=True
        )[:5]
        
        for i, (mint, data) in enumerate(sorted_tokens, 1):
            print(f"  {i}. {data['name']} - {data['volume']:.2f} SOL ({data['trades']} trades)")
    
    def process_message(self, message):
        """Process incoming WebSocket message."""
        try:
            data = json.loads(message)
            current_time = datetime.now().strftime("%H:%M:%S")
            
            # Check if it's a new token
            if self.is_new_token(data):
                self.process_new_token(data, current_time)
            
            # Check if it's a trade
            if self.is_trade(data):
                self.process_trade(data, current_time)
                
        except json.JSONDecodeError:
            pass  # Ignore non-JSON messages
        except Exception as e:
            print(f"Error processing message: {e}")
    
    def is_new_token(self, data):
        """Check if message represents a new token."""
        return (
            isinstance(data, dict) and
            'mint' in data and
            'name' in data and
            'symbol' in data and
            'initialBuy' in data and
            data.get('initialBuy') is True
        )
    
    def is_trade(self, data):
        """Check if message represents a trade."""
        return (
            isinstance(data, dict) and
            'signature' in data and
            'mint' in data and
            'traderPublicKey' in data and
            'solAmount' in data
        )
    
    def process_new_token(self, data, current_time):
        """Process a new token launch."""
        mint = data.get('mint', '')
        name = data.get('name', 'Unknown')
        symbol = data.get('symbol', 'Unknown')
        
        if mint not in self.stats['tokens_seen']:
            self.stats['tokens_seen'].add(mint)
            self.stats['new_tokens'] += 1
            
            token_info = {
                'mint': mint,
                'name': name,
                'symbol': symbol,
                'time': current_time
            }
            self.recent_tokens.append(token_info)
            
            print(f"\n🪙 NEW TOKEN: {name} ({symbol}) at {current_time}")
            print(f"   Mint: {mint[:20]}...")
    
    def process_trade(self, data, current_time):
        """Process a trade."""
        mint = data.get('mint', '')
        trader = data.get('traderPublicKey', '')
        sol_amount = float(data.get('solAmount', 0))
        is_buy = data.get('txType') == 'buy'
        token_name = data.get('name', data.get('symbol', 'Unknown'))
        
        # Update statistics
        self.stats['total_trades'] += 1
        self.stats['total_volume_sol'] += sol_amount
        self.stats['unique_traders'].add(trader)
        
        # Update token volume
        self.top_tokens[mint]['volume'] += sol_amount
        self.top_tokens[mint]['trades'] += 1
        self.top_tokens[mint]['name'] = token_name
        
        # Track large trades
        if sol_amount >= 5.0:
            self.stats['large_trades'] += 1
        if sol_amount >= 20.0:
            self.stats['whale_trades'] += 1
        
        # Store significant trades
        if sol_amount >= 1.0:
            trade_info = {
                'mint': mint,
                'trader': trader[:8] + '...',
                'amount': sol_amount,
                'is_buy': is_buy,
                'token': token_name,
                'time': current_time
            }
            self.recent_trades.append(trade_info)
            
            # Print large trades immediately
            if sol_amount >= 5.0:
                action = "🟢 BUY" if is_buy else "🔴 SELL"
                whale_flag = "🐋 " if sol_amount >= 20.0 else ""
                print(f"\n{whale_flag}💰 LARGE TRADE: {action} {sol_amount:.2f} SOL of {token_name} at {current_time}")
                print(f"   Trader: {trader[:20]}...")
    
    async def run_monitor(self):
        """Run the live monitor for 1 hour."""
        self.print_header()
        self.is_running = True
        
        try:
            async with websockets.connect(
                "wss://pumpportal.fun/api/data",
                ping_interval=20,
                ping_timeout=10
            ) as websocket:
                print("✅ Connected to PumpPortal WebSocket")
                
                # Subscribe to new tokens
                await websocket.send(json.dumps({"method": "subscribeNewToken"}))
                response = await websocket.recv()
                print(f"📡 Subscription: {response}")
                
                print("\n🔄 Starting live monitoring... (Press Ctrl+C to stop early)")
                print("="*80)
                
                last_stats_update = time.time()
                message_count = 0
                
                while self.is_running:
                    # Check if session time is up
                    elapsed_seconds = (datetime.now() - self.start_time).total_seconds()
                    if elapsed_seconds >= self.session_duration:
                        print(f"\n⏰ 1-hour session completed!")
                        break
                    
                    try:
                        # Receive message with timeout
                        message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                        message_count += 1
                        
                        # Process the message
                        self.process_message(message)
                        
                        # Update stats display every 30 seconds
                        current_time = time.time()
                        if current_time - last_stats_update >= 30:
                            self.print_stats()
                            self.print_recent_activity()
                            self.print_top_tokens()
                            print("="*80)
                            last_stats_update = current_time
                        
                    except asyncio.TimeoutError:
                        # No message received, continue
                        continue
                    except websockets.exceptions.ConnectionClosed:
                        print("🔌 WebSocket connection closed, attempting to reconnect...")
                        break
                        
        except KeyboardInterrupt:
            print(f"\n👋 Session stopped by user")
        except Exception as e:
            print(f"❌ Error: {e}")
        finally:
            self.is_running = False
            self.print_final_summary()
    
    def print_final_summary(self):
        """Print final session summary."""
        elapsed = self.format_time_elapsed()
        
        print("\n" + "="*80)
        print("📋 FINAL SESSION SUMMARY")
        print("="*80)
        print(f"⏰ Session Duration: {elapsed}")
        print(f"🪙 New Tokens: {self.stats['new_tokens']}")
        print(f"💰 Total Trades: {self.stats['total_trades']}")
        print(f"💎 Total Volume: {self.stats['total_volume_sol']:.2f} SOL")
        print(f"👥 Unique Traders: {len(self.stats['unique_traders'])}")
        print(f"🔥 Large Trades: {self.stats['large_trades']}")
        print(f"🐋 Whale Trades: {self.stats['whale_trades']}")
        
        if self.stats['total_trades'] > 0:
            avg_trade = self.stats['total_volume_sol'] / self.stats['total_trades']
            trades_per_minute = self.stats['total_trades'] / (int(elapsed.split(':')[1]) + 1)
            print(f"📈 Average Trade: {avg_trade:.4f} SOL")
            print(f"⚡ Trades/Minute: {trades_per_minute:.1f}")
        
        self.print_top_tokens()
        print("="*80)
        print("🎯 Live monitoring session completed!")


async def main():
    """Main function."""
    monitor = LivePumpMonitor()
    
    # Set up signal handler for graceful shutdown
    def signal_handler(signum, frame):
        print(f"\n🛑 Received signal {signum}, stopping monitor...")
        monitor.is_running = False
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    await monitor.run_monitor()


if __name__ == "__main__":
    print("🚀 Starting Live Pump.fun Monitor...")
    asyncio.run(main())
