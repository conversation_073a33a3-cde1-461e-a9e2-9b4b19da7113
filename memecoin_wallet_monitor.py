#!/usr/bin/env python3
"""
Memecoin Wallet Monitor - Real-time monitoring of memecoin insider wallets.
Tracks new transactions and potential memecoin entries.
"""

import asyncio
import aiohttp
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass


@dataclass
class WalletTransaction:
    """Represents a wallet transaction."""
    signature: str
    wallet_address: str
    timestamp: datetime
    transaction_type: str  # 'buy', 'sell', 'transfer'
    amount_sol: float
    token_mint: Optional[str] = None
    token_symbol: Optional[str] = None
    is_new_token: bool = False
    minutes_since_launch: Optional[float] = None


class MemecoinWalletMonitor:
    """Monitors memecoin insider wallets for new activity."""
    
    def __init__(self):
        self.session = None
        self.monitored_wallets = []
        self.last_check_time = {}
        self.solana_rpc = "https://api.mainnet-beta.solana.com"
        
        # Monitoring settings
        self.check_interval_seconds = 30  # Check every 30 seconds
        self.min_transaction_amount = 0.1  # Minimum SOL amount to alert
        self.new_token_threshold_hours = 24  # Consider token "new" if < 24h old
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=15),
            headers={"Content-Type": "application/json"}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    def load_memecoin_insider_wallets(self, filename: str = "memecoin_insider_wallets.json"):
        """Load memecoin insider wallets from file."""
        try:
            with open(filename, 'r') as f:
                data = json.load(f)
                
            wallets = []
            for wallet_data in data.get('memecoin_insider_wallets', []):
                wallets.append(wallet_data['wallet_address'])
            
            self.monitored_wallets = wallets
            print(f"✅ Loaded {len(wallets)} memecoin insider wallets for monitoring")
            
            # Initialize last check times
            for wallet in wallets:
                self.last_check_time[wallet] = datetime.now() - timedelta(minutes=5)
            
            return len(wallets) > 0
            
        except FileNotFoundError:
            print(f"❌ File {filename} not found")
            return False
        except Exception as e:
            print(f"❌ Error loading wallets: {e}")
            return False
    
    async def start_monitoring(self):
        """Start real-time monitoring of memecoin insider wallets."""
        if not self.monitored_wallets:
            print("❌ No wallets to monitor. Load wallets first.")
            return
        
        print("🚀 MEMECOIN WALLET MONITOR STARTED")
        print("=" * 60)
        print(f"📊 Monitoring {len(self.monitored_wallets)} insider wallets")
        print(f"⏰ Check interval: {self.check_interval_seconds} seconds")
        print(f"💰 Min alert amount: {self.min_transaction_amount} SOL")
        print("🔍 Looking for new memecoin entries...")
        print("\nPress Ctrl+C to stop monitoring\n")
        
        try:
            while True:
                print(f"🔄 Checking wallets at {datetime.now().strftime('%H:%M:%S')}")
                
                # Check each wallet for new transactions
                new_transactions = []
                for wallet in self.monitored_wallets:
                    wallet_txs = await self._check_wallet_transactions(wallet)
                    new_transactions.extend(wallet_txs)
                
                # Process and alert on new transactions
                if new_transactions:
                    await self._process_new_transactions(new_transactions)
                else:
                    print("   📭 No new significant transactions found")
                
                # Wait before next check
                await asyncio.sleep(self.check_interval_seconds)
                
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped by user")
        except Exception as e:
            print(f"❌ Monitoring error: {e}")
    
    async def _check_wallet_transactions(self, wallet_address: str) -> List[WalletTransaction]:
        """Check for new transactions from a specific wallet."""
        try:
            # Get recent signatures for the wallet
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getSignaturesForAddress",
                "params": [
                    wallet_address,
                    {
                        "limit": 10,
                        "commitment": "confirmed"
                    }
                ]
            }
            
            async with self.session.post(self.solana_rpc, json=payload) as response:
                if response.status != 200:
                    return []
                
                data = await response.json()
                signatures = data.get('result', [])
                
                new_transactions = []
                last_check = self.last_check_time.get(wallet_address, datetime.now() - timedelta(hours=1))
                
                for sig_info in signatures:
                    # Check if transaction is new
                    block_time = sig_info.get('blockTime')
                    if not block_time:
                        continue
                    
                    tx_time = datetime.fromtimestamp(block_time)
                    if tx_time <= last_check:
                        continue
                    
                    # Get transaction details
                    tx_details = await self._get_transaction_details(sig_info['signature'])
                    if tx_details:
                        new_transactions.append(tx_details)
                
                # Update last check time
                self.last_check_time[wallet_address] = datetime.now()
                
                return new_transactions
                
        except Exception as e:
            print(f"   ⚠️ Error checking wallet {wallet_address[:8]}...: {e}")
            return []
    
    async def _get_transaction_details(self, signature: str) -> Optional[WalletTransaction]:
        """Get detailed information about a transaction."""
        try:
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getTransaction",
                "params": [
                    signature,
                    {
                        "encoding": "json",
                        "commitment": "confirmed",
                        "maxSupportedTransactionVersion": 0
                    }
                ]
            }
            
            async with self.session.post(self.solana_rpc, json=payload) as response:
                if response.status != 200:
                    return None
                
                data = await response.json()
                result = data.get('result')
                
                if not result:
                    return None
                
                # Extract transaction information
                meta = result.get('meta', {})
                transaction = result.get('transaction', {})
                block_time = result.get('blockTime')
                
                if not block_time:
                    return None
                
                # Get wallet address (first signer)
                message = transaction.get('message', {})
                account_keys = message.get('accountKeys', [])
                
                if not account_keys:
                    return None
                
                wallet_address = account_keys[0]
                
                # Calculate SOL change
                pre_balances = meta.get('preBalances', [])
                post_balances = meta.get('postBalances', [])
                
                if len(pre_balances) > 0 and len(post_balances) > 0:
                    sol_change = (post_balances[0] - pre_balances[0]) / 1e9
                    
                    # Determine transaction type and amount
                    if abs(sol_change) < self.min_transaction_amount:
                        return None
                    
                    tx_type = "buy" if sol_change < 0 else "sell"
                    amount_sol = abs(sol_change)
                    
                    return WalletTransaction(
                        signature=signature,
                        wallet_address=wallet_address,
                        timestamp=datetime.fromtimestamp(block_time),
                        transaction_type=tx_type,
                        amount_sol=amount_sol,
                        token_mint=None,  # Would need to parse from transaction
                        token_symbol=None,
                        is_new_token=False  # Would need to check token age
                    )
                
                return None
                
        except Exception as e:
            print(f"   ⚠️ Error getting transaction details: {e}")
            return None
    
    async def _process_new_transactions(self, transactions: List[WalletTransaction]):
        """Process and alert on new transactions."""
        print(f"🚨 Found {len(transactions)} new transactions!")
        
        for tx in transactions:
            print(f"\n💰 NEW TRANSACTION ALERT")
            print(f"   📍 Wallet: {tx.wallet_address[:8]}...{tx.wallet_address[-8:]}")
            print(f"   🔄 Type: {tx.transaction_type.upper()}")
            print(f"   💵 Amount: {tx.amount_sol:.4f} SOL")
            print(f"   ⏰ Time: {tx.timestamp.strftime('%H:%M:%S')}")
            print(f"   🔗 Signature: {tx.signature}")
            print(f"   🌐 Solscan: https://solscan.io/tx/{tx.signature}")
            
            # Special alerts for large transactions
            if tx.amount_sol >= 5.0:
                print(f"   🚨 LARGE TRANSACTION ALERT: {tx.amount_sol:.2f} SOL!")
            
            if tx.transaction_type == "buy" and tx.amount_sol >= 1.0:
                print(f"   🎯 POTENTIAL MEMECOIN ENTRY: {tx.amount_sol:.2f} SOL buy!")
    
    async def run_single_check(self):
        """Run a single check of all wallets (for testing)."""
        if not self.monitored_wallets:
            print("❌ No wallets to monitor. Load wallets first.")
            return
        
        print("🔍 Running single wallet check...")
        print("=" * 50)
        
        all_transactions = []
        for wallet in self.monitored_wallets[:5]:  # Check first 5 wallets
            print(f"Checking {wallet[:8]}...{wallet[-8:]}")
            txs = await self._check_wallet_transactions(wallet)
            all_transactions.extend(txs)
            await asyncio.sleep(0.5)  # Rate limiting
        
        if all_transactions:
            await self._process_new_transactions(all_transactions)
        else:
            print("📭 No new transactions found in recent check")


async def main():
    """Main monitoring function."""
    print("🚀 MEMECOIN INSIDER WALLET MONITOR")
    print("=" * 60)
    
    async with MemecoinWalletMonitor() as monitor:
        # Load wallets
        if not monitor.load_memecoin_insider_wallets():
            print("❌ Failed to load wallets. Exiting.")
            return
        
        print("\nChoose monitoring mode:")
        print("1. Real-time monitoring (continuous)")
        print("2. Single check (test)")
        
        try:
            choice = input("Enter choice (1 or 2): ").strip()
            
            if choice == "1":
                await monitor.start_monitoring()
            elif choice == "2":
                await monitor.run_single_check()
            else:
                print("❌ Invalid choice")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")


if __name__ == "__main__":
    asyncio.run(main())
