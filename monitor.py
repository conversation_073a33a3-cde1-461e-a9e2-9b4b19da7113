#!/usr/bin/env python3
"""
Simple monitoring dashboard for the Solana Trading Bot.
"""

import asyncio
import sys
import time
from pathlib import Path
from datetime import datetime, timedelta

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))


async def get_bot_status():
    """Get current bot status."""
    try:
        from src.data.database import db_manager, WalletRepository, CopyTradeRepository
        from src.trading.risk_manager import risk_manager
        from src.wallet_tracker.pumpfun_analyzer import pumpfun_analyzer

        await db_manager.initialize()

        wallet_repo = WalletRepository(db_manager)
        copy_trade_repo = CopyTradeRepository(db_manager)

        # Get active wallets
        active_wallets = await wallet_repo.get_active_wallets()
        copy_trading_wallets = await wallet_repo.get_copy_trading_wallets()
        top_performers = await wallet_repo.get_top_performers(limit=10)

        # Get risk metrics
        risk_metrics = await risk_manager.get_risk_metrics()

        # Get pending copy trades
        pending_trades = await copy_trade_repo.get_pending_copy_trades()

        # Get pump.fun statistics
        pump_stats = await pumpfun_analyzer.get_pump_statistics()

        await db_manager.close()

        return {
            "active_wallets": len(active_wallets),
            "copy_trading_wallets": len(copy_trading_wallets),
            "top_performers": top_performers,
            "risk_metrics": risk_metrics,
            "pending_trades": len(pending_trades),
            "pump_stats": pump_stats,
        }

    except Exception as e:
        print(f"Error getting bot status: {e}")
        return None


def print_dashboard(status):
    """Print the monitoring dashboard."""
    # Clear screen
    print("\033[2J\033[H")

    print("🤖 Solana Trading Bot - Live Dashboard")
    print("=" * 60)
    print(f"📅 Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    if not status:
        print("❌ Unable to fetch bot status")
        return

    # Wallet Statistics
    print("📊 WALLET STATISTICS")
    print("-" * 30)
    print(f"Active Wallets: {status['active_wallets']}")
    print(f"Copy Trading Wallets: {status['copy_trading_wallets']}")
    print(f"Pending Trades: {status['pending_trades']}")
    print()

    # Pump.fun Statistics
    pump_stats = status.get("pump_stats", {})
    print("🚀 PUMP.FUN STATISTICS")
    print("-" * 30)
    print(f"Tracked Wallets: {pump_stats.get('total_tracked_wallets', 0)}")
    print(f"Total Trades: {pump_stats.get('total_pump_trades', 0)}")
    print(f"Snipe Attempts: {pump_stats.get('total_snipe_attempts', 0)}")
    print(f"Tokens Launched (24h): {pump_stats.get('tokens_launched_24h', 0)}")
    print()

    # Risk Metrics
    risk = status["risk_metrics"]
    print("🛡️  RISK METRICS")
    print("-" * 30)
    print(f"Daily P&L: {risk.daily_pnl:.4f} SOL")
    print(f"Daily Loss: {risk.daily_loss:.4f} SOL")
    print(f"Active Positions: {risk.active_positions}")
    print(f"24h Win Rate: {risk.win_rate_24h:.2%}")
    print(f"Total Exposure: {risk.total_exposure:.4f} SOL")
    print()

    # Top Performers
    print("🏆 TOP PERFORMERS")
    print("-" * 30)
    for i, wallet in enumerate(status["top_performers"][:5], 1):
        trader_type = wallet.trader_type or "unknown"
        print(f"{i}. {wallet.address[:8]}... ({trader_type})")
        print(
            f"   ROI: {wallet.total_roi:.1f}x | Win Rate: {wallet.win_rate:.1f}% | Trades: {wallet.total_trades}"
        )

    print()
    print("🔄 Refreshing in 30 seconds... (Ctrl+C to exit)")


async def monitor_loop():
    """Main monitoring loop."""
    while True:
        try:
            status = await get_bot_status()
            print_dashboard(status)
            await asyncio.sleep(30)  # Update every 30 seconds

        except KeyboardInterrupt:
            print("\n👋 Monitoring stopped")
            break
        except Exception as e:
            print(f"❌ Monitoring error: {e}")
            await asyncio.sleep(10)


def show_help():
    """Show help information."""
    print("🤖 Solana Trading Bot Monitor")
    print("=" * 40)
    print()
    print("Usage: python monitor.py [command]")
    print()
    print("Commands:")
    print("  status    - Show current bot status (default)")
    print("  wallets   - Show tracked wallets")
    print("  trades    - Show recent trades")
    print("  risk      - Show risk metrics")
    print("  pump      - Show pump.fun statistics")
    print("  help      - Show this help")
    print()


async def show_wallets():
    """Show tracked wallets."""
    try:
        from src.data.database import db_manager, WalletRepository

        await db_manager.initialize()
        wallet_repo = WalletRepository(db_manager)

        active_wallets = await wallet_repo.get_active_wallets()

        print("📋 TRACKED WALLETS")
        print("=" * 60)

        for wallet in active_wallets[:20]:  # Show top 20
            trader_type = wallet.trader_type or "unknown"
            copy_status = "✅" if wallet.is_copy_trading else "❌"

            print(f"{wallet.address}")
            print(f"  Type: {trader_type} | Copy Trading: {copy_status}")
            print(f"  ROI: {wallet.total_roi:.1f}x | Win Rate: {wallet.win_rate:.1f}%")
            print(
                f"  Trades: {wallet.total_trades} | Confidence: {wallet.confidence_score:.2f}"
            )
            print()

        await db_manager.close()

    except Exception as e:
        print(f"❌ Error showing wallets: {e}")


async def show_risk():
    """Show detailed risk metrics."""
    try:
        from src.trading.risk_manager import risk_manager

        risk_metrics = await risk_manager.get_risk_metrics()
        position_summary = await risk_manager.get_position_summary()

        print("🛡️  RISK MANAGEMENT DASHBOARD")
        print("=" * 60)

        print("📊 Daily Metrics:")
        print(f"  P&L: {risk_metrics.daily_pnl:.4f} SOL")
        print(f"  Loss: {risk_metrics.daily_loss:.4f} SOL")
        print(f"  Win Rate: {risk_metrics.win_rate_24h:.2%}")
        print()

        print("📈 Position Metrics:")
        print(f"  Active Positions: {risk_metrics.active_positions}")
        print(f"  Total Exposure: {risk_metrics.total_exposure:.4f} SOL")
        print(f"  Max Position Size: {risk_metrics.max_position_size:.4f} SOL")
        print()

        print("🚨 Safety Status:")
        emergency = position_summary.get("emergency_stop", False)
        print(f"  Emergency Stop: {'🔴 ACTIVE' if emergency else '🟢 NORMAL'}")
        print()

        if position_summary.get("positions"):
            print("💼 Active Positions:")
            for pos in position_summary["positions"][:10]:
                print(f"  {pos['token_mint'][:8]}...")
                print(
                    f"    Entry: {pos['entry_price']:.6f} | Amount: {pos['amount_sol']:.4f} SOL"
                )
                print(f"    Hold Time: {pos['hold_time_hours']:.1f}h")

    except Exception as e:
        print(f"❌ Error showing risk metrics: {e}")


async def show_pump_stats():
    """Show detailed pump.fun statistics."""
    try:
        from src.wallet_tracker.pumpfun_analyzer import pumpfun_analyzer

        # Get pump.fun statistics
        pump_stats = await pumpfun_analyzer.get_pump_statistics()
        top_snipers = await pumpfun_analyzer.get_top_pump_snipers(limit=10)

        print("🚀 PUMP.FUN DASHBOARD")
        print("=" * 60)

        print("📊 Overall Statistics:")
        print(f"  Tracked Wallets: {pump_stats.get('total_tracked_wallets', 0)}")
        print(f"  Total Trades: {pump_stats.get('total_pump_trades', 0)}")
        print(f"  Snipe Attempts: {pump_stats.get('total_snipe_attempts', 0)}")
        print(f"  Tokens Launched (24h): {pump_stats.get('tokens_launched_24h', 0)}")
        print(
            f"  Avg Trades per Wallet: {pump_stats.get('avg_trades_per_wallet', 0):.1f}"
        )
        print()

        if top_snipers:
            print("🎯 TOP PUMP.FUN SNIPERS:")
            for i, sniper in enumerate(top_snipers, 1):
                print(f"{i}. {sniper['address'][:8]}...")
                print(f"   Success Rate: {sniper['snipe_success_rate']:.2%}")
                print(f"   Avg Entry Speed: {sniper['avg_entry_speed']:.1f}s")
                print(f"   Best ROI: {sniper['best_roi']:.1f}x")
                print(f"   Total Trades: {sniper['total_trades']}")
                print(f"   P&L: {sniper['total_pnl']:.4f} SOL")
                print()
        else:
            print("No pump.fun snipers found yet.")

    except Exception as e:
        print(f"❌ Error showing pump.fun stats: {e}")


async def main():
    """Main function."""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()

        if command == "help":
            show_help()
        elif command == "wallets":
            await show_wallets()
        elif command == "risk":
            await show_risk()
        elif command == "pump":
            await show_pump_stats()
        elif command == "status":
            await monitor_loop()
        else:
            print(f"❌ Unknown command: {command}")
            show_help()
    else:
        # Default to status monitoring
        await monitor_loop()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Monitor stopped")
    except Exception as e:
        print(f"❌ Monitor error: {e}")
        sys.exit(1)
