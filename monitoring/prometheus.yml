# Prometheus configuration for Solana Trading Bot monitoring

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Trading bot metrics
  - job_name: 'solana-trading-bot'
    static_configs:
      - targets: ['solana-trading-bot:8080']
    scrape_interval: 30s
    metrics_path: /metrics
    
  # System metrics (if node_exporter is available)
  - job_name: 'node'
    static_configs:
      - targets: ['localhost:9100']
    scrape_interval: 30s
    
  # Docker metrics (if available)
  - job_name: 'docker'
    static_configs:
      - targets: ['localhost:9323']
    scrape_interval: 30s

# Alerting configuration (optional)
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093
