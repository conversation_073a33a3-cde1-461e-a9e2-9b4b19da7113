#!/usr/bin/env python3
"""
Dedicated pump.fun scanner for real-time monitoring of new token launches and snipe detection.
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))


async def main():
    """Main pump.fun scanner function."""
    print("🚀 Starting Pump.fun Scanner...")
    print("=" * 50)
    
    try:
        from src.blockchain.pumpfun_monitor import pumpfun_monitor
        from src.wallet_tracker.pumpfun_analyzer import pumpfun_analyzer
        from src.data.database import db_manager
        
        # Initialize database
        await db_manager.initialize()
        print("✅ Database initialized")
        
        # Initialize pump.fun analyzer
        await pumpfun_analyzer.initialize()
        print("✅ Pump.fun analyzer initialized")
        
        # Set up callbacks for real-time monitoring
        def on_new_token(token):
            print(f"\n🆕 NEW TOKEN LAUNCHED!")
            print(f"   Symbol: {token.symbol}")
            print(f"   Name: {token.name}")
            print(f"   Creator: {token.creator[:8]}...")
            print(f"   Market Cap: {token.market_cap:.4f} SOL")
            print(f"   Time: {token.creation_time.strftime('%H:%M:%S')}")
        
        def on_new_trade(trade, token, is_early):
            if is_early:
                time_since_launch = (trade.timestamp - token.creation_time).total_seconds()
                print(f"\n⚡ EARLY TRADE DETECTED!")
                print(f"   Token: {token.symbol}")
                print(f"   Trader: {trade.trader[:8]}...")
                print(f"   Type: {'BUY' if trade.is_buy else 'SELL'}")
                print(f"   Amount: {trade.sol_amount:.4f} SOL")
                print(f"   Speed: {time_since_launch:.1f}s after launch")
                
                if time_since_launch <= 60:  # Snipe threshold
                    print(f"   🎯 POTENTIAL SNIPE!")
        
        # Add callbacks
        pumpfun_monitor.add_token_callback(on_new_token)
        pumpfun_monitor.add_trade_callback(on_new_trade)
        
        print("✅ Callbacks registered")
        print("\n🔍 Starting pump.fun monitoring...")
        print("Press Ctrl+C to stop\n")
        
        # Start monitoring
        await pumpfun_monitor.start_monitoring()
        
    except KeyboardInterrupt:
        print("\n👋 Scanner stopped by user")
    except Exception as e:
        print(f"❌ Scanner error: {e}")
        sys.exit(1)
    finally:
        # Cleanup
        try:
            await pumpfun_monitor.stop_monitoring()
            await db_manager.close()
        except:
            pass


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Scanner stopped")
    except Exception as e:
        print(f"❌ Scanner failed: {e}")
        sys.exit(1)
