#!/usr/bin/env python3
"""
Run the Solana Trading Bot without Telegram functionality.
This bypasses the Telegram initialization issues.
"""

import asyncio
import signal
import sys
from datetime import datetime

# Import core components
from src.config.settings import settings
from src.data.database import db_manager
from src.blockchain.transaction_monitor import transaction_monitor
from src.blockchain.pumpfun_monitor import pumpfun_monitor
from src.wallet_tracker.wallet_analyzer import wallet_analyzer
from src.wallet_tracker.trader_classifier import trader_classifier
from src.wallet_tracker.pumpfun_analyzer import pumpfun_analyzer
from src.trading.copy_trader import copy_trader
from src.trading.risk_manager import risk_manager
from src.utils.logger import main_logger, get_logger


class SolanaTradingBotNoTelegram:
    """Solana Trading Bot without Telegram functionality."""

    def __init__(self):
        self.logger = get_logger("SolanaTradingBot")
        self.is_running = False
        self.tasks = []

    async def initialize(self):
        """Initialize the trading bot."""
        try:
            self.logger.info("Initializing Solana Trading Bot (No Telegram)...")

            # Initialize database
            await db_manager.initialize()
            self.logger.info("Database initialized")

            # Validate configuration
            self._validate_configuration()

            # Initialize pump.fun analyzer
            await pumpfun_analyzer.initialize()

            self.logger.info("Bot initialization completed successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize bot: {e}")
            raise

    async def start(self):
        """Start the trading bot."""
        try:
            if self.is_running:
                self.logger.warning("Bot is already running")
                return

            self.logger.info("Starting Solana Trading Bot...")

            # Start transaction monitoring
            if settings.features.enable_wallet_discovery:
                self.tasks.append(
                    asyncio.create_task(transaction_monitor.start_monitoring())
                )
                self.logger.info("Transaction monitoring started")

            # Start pump.fun monitoring
            if settings.monitoring.pumpfun_monitoring.enabled:
                self.tasks.append(
                    asyncio.create_task(pumpfun_monitor.start_monitoring())
                )
                self.logger.info("Pump.fun monitoring started")

            # Start periodic tasks
            self.tasks.append(asyncio.create_task(self._periodic_wallet_analysis()))
            self.tasks.append(asyncio.create_task(self._periodic_risk_monitoring()))

            if settings.features.enable_copy_trading:
                self.tasks.append(
                    asyncio.create_task(self._periodic_position_monitoring())
                )

            # Start performance tracking
            if settings.features.enable_performance_tracking:
                self.tasks.append(
                    asyncio.create_task(self._periodic_performance_update())
                )

            self.is_running = True
            self.logger.info("Solana Trading Bot started successfully")
            self.logger.info(f"Running {len(self.tasks)} background tasks")

            # Wait for all tasks
            await asyncio.gather(*self.tasks, return_exceptions=True)

        except Exception as e:
            self.logger.error(f"Error starting bot: {e}")
            await self.stop()
            raise

    async def stop(self):
        """Stop the trading bot."""
        try:
            self.logger.info("Stopping Solana Trading Bot...")

            self.is_running = False

            # Cancel all tasks
            for task in self.tasks:
                if not task.done():
                    task.cancel()

            # Wait for tasks to complete
            if self.tasks:
                await asyncio.gather(*self.tasks, return_exceptions=True)

            # Stop monitoring services
            await transaction_monitor.stop_monitoring()
            await pumpfun_monitor.stop_monitoring()

            # Close database connections
            await db_manager.close()

            self.logger.info("Solana Trading Bot stopped")

        except Exception as e:
            self.logger.error(f"Error stopping bot: {e}")

    async def _periodic_wallet_analysis(self):
        """Periodically analyze and update wallet performance."""
        try:
            while self.is_running:
                try:
                    self.logger.info("Running periodic wallet analysis...")
                    await asyncio.sleep(
                        settings.monitoring.performance_monitoring.update_interval_seconds
                    )
                except Exception as e:
                    self.logger.error(f"Error in periodic wallet analysis: {e}")
                    await asyncio.sleep(60)
        except asyncio.CancelledError:
            self.logger.info("Periodic wallet analysis task cancelled")

    async def _periodic_risk_monitoring(self):
        """Periodically monitor risk metrics."""
        try:
            while self.is_running:
                try:
                    # Get current risk metrics
                    risk_metrics = await risk_manager.get_risk_metrics()

                    # Log risk metrics
                    self.logger.info(
                        f"Risk metrics - Daily P&L: {risk_metrics.daily_pnl:.4f} SOL, "
                        f"Active positions: {risk_metrics.active_positions}, "
                        f"Win rate: {risk_metrics.win_rate_24h:.2%}"
                    )

                    # Check for risk alerts
                    if (
                        risk_metrics.daily_loss
                        > settings.trading.risk_management.max_daily_loss_sol * 0.8
                    ):
                        self.logger.warning(
                            f"High daily loss detected: {risk_metrics.daily_loss} SOL"
                        )

                    await asyncio.sleep(300)  # Check every 5 minutes

                except Exception as e:
                    self.logger.error(f"Error in risk monitoring: {e}")
                    await asyncio.sleep(60)

        except asyncio.CancelledError:
            self.logger.info("Risk monitoring task cancelled")

    async def _periodic_position_monitoring(self):
        """Periodically monitor active positions for stop loss/take profit."""
        try:
            while self.is_running:
                try:
                    await copy_trader.monitor_active_positions()
                    await asyncio.sleep(30)  # Check every 30 seconds
                except Exception as e:
                    self.logger.error(f"Error in position monitoring: {e}")
                    await asyncio.sleep(60)
        except asyncio.CancelledError:
            self.logger.info("Position monitoring task cancelled")

    async def _periodic_performance_update(self):
        """Periodically update performance metrics."""
        try:
            while self.is_running:
                try:
                    self.logger.info("Updating performance metrics...")

                    # Get copy trading performance
                    copy_performance = await copy_trader.get_copy_trade_performance()

                    # Get risk metrics
                    risk_metrics = await risk_manager.get_risk_metrics()

                    # Log performance summary
                    self.logger.info(
                        f"Performance summary - "
                        f"Copy trades: {copy_performance.get('total_copy_trades', 0)}, "
                        f"Success rate: {copy_performance.get('win_rate', 0):.2%}, "
                        f"Daily P&L: {risk_metrics.daily_pnl:.4f} SOL"
                    )

                    await asyncio.sleep(3600)  # Update every hour

                except Exception as e:
                    self.logger.error(f"Error updating performance: {e}")
                    await asyncio.sleep(300)

        except asyncio.CancelledError:
            self.logger.info("Performance update task cancelled")

    def _validate_configuration(self):
        """Validate bot configuration."""
        try:
            # Check required settings
            if not settings.solana.rpc_url:
                raise ValueError("Solana RPC URL not configured")

            if settings.trading.risk_management.max_daily_loss_sol <= 0:
                raise ValueError("Max daily loss must be greater than 0")

            if settings.trading.copy_trading.max_position_size_sol <= 0:
                raise ValueError("Max position size must be greater than 0")

            # Validate wallet tracking thresholds
            if settings.wallet_tracking.min_roi_threshold <= 0:
                raise ValueError("Minimum ROI threshold must be greater than 0")

            self.logger.info("Configuration validation passed")

        except Exception as e:
            self.logger.error(f"Configuration validation failed: {e}")
            raise


async def main():
    """Main function to run the bot."""
    bot = SolanaTradingBotNoTelegram()

    # Set up signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        main_logger.info(f"Received signal {signum}, shutting down...")
        asyncio.create_task(bot.stop())

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        # Initialize and start the bot
        await bot.initialize()
        await bot.start()

    except KeyboardInterrupt:
        main_logger.info("Received keyboard interrupt")
    except Exception as e:
        main_logger.error(f"Bot crashed: {e}")
        sys.exit(1)
    finally:
        await bot.stop()


if __name__ == "__main__":
    # Run the bot
    main_logger.info("🚀 Starting Solana Trading Bot (No Telegram)...")
    main_logger.info("="*60)
    asyncio.run(main())
