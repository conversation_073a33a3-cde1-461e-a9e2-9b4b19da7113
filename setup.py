#!/usr/bin/env python3
"""
Setup script for the Solana Trading Bot.
"""

import os
import sys
import asyncio
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))


def create_directories():
    """Create necessary directories."""
    directories = [
        "logs",
        "data",
        "backups"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ Created directory: {directory}")


def create_env_file():
    """Create .env file from template if it doesn't exist."""
    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            import shutil
            shutil.copy(".env.example", ".env")
            print("✅ Created .env file from template")
            print("⚠️  Please edit .env file with your configuration")
        else:
            print("❌ .env.example not found")
    else:
        print("✅ .env file already exists")


async def initialize_database():
    """Initialize the database."""
    try:
        from src.data.database import db_manager
        await db_manager.initialize()
        print("✅ Database initialized successfully")
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False
    finally:
        await db_manager.close()
    
    return True


def validate_dependencies():
    """Validate that all required dependencies are installed."""
    required_packages = [
        "solana",
        "aiohttp",
        "sqlalchemy",
        "pydantic",
        "structlog",
        "websockets"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} is missing")
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Please run: pip install -r requirements.txt")
        return False
    
    return True


def check_configuration():
    """Check if configuration is valid."""
    try:
        from src.config.settings import settings
        
        # Basic validation
        if not settings.solana.rpc_url:
            print("❌ Solana RPC URL not configured")
            return False
        
        if settings.trading.risk_management.max_daily_loss_sol <= 0:
            print("❌ Invalid max daily loss configuration")
            return False
        
        print("✅ Configuration validation passed")
        return True
        
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")
        return False


async def main():
    """Main setup function."""
    print("🔧 Setting up Solana Trading Bot...")
    print("=" * 50)
    
    # Step 1: Create directories
    print("\n📁 Creating directories...")
    create_directories()
    
    # Step 2: Create .env file
    print("\n📝 Setting up environment file...")
    create_env_file()
    
    # Step 3: Validate dependencies
    print("\n📦 Validating dependencies...")
    if not validate_dependencies():
        print("\n❌ Setup failed: Missing dependencies")
        return False
    
    # Step 4: Check configuration
    print("\n⚙️  Validating configuration...")
    if not check_configuration():
        print("\n❌ Setup failed: Invalid configuration")
        return False
    
    # Step 5: Initialize database
    print("\n🗄️  Initializing database...")
    if not await initialize_database():
        print("\n❌ Setup failed: Database initialization failed")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Edit .env file with your Solana private key and RPC URL")
    print("2. Review config.yaml for trading parameters")
    print("3. Run the bot with: python run_bot.py")
    print("\n⚠️  Important:")
    print("- Test on devnet first before using mainnet")
    print("- Start with small position sizes")
    print("- Monitor the bot closely")
    print("- Never invest more than you can afford to lose")
    
    return True


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        if not success:
            sys.exit(1)
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        sys.exit(1)
