"""
Solana blockchain client for the trading bot.
"""

import asyncio
import json
from typing import Optional, Dict, Any, List, Callable
from datetime import datetime
import aiohttp
import websockets
from solana.rpc.async_api import AsyncClient
from solana.rpc.commitment import Commitment
from solana.rpc.types import TxOpts
from solders.pubkey import Pubkey as PublicKey
from solders.transaction import Transaction
from solders.signature import Signature

from src.config.settings import settings
from src.utils.logger import LoggerMixin
from src.utils.helpers import retry_async


class SolanaClient(LoggerMixin):
    """Async Solana RPC client with WebSocket support."""

    def __init__(self):
        super().__init__()
        self.rpc_client = None
        self.ws_connection = None
        self.subscription_callbacks = {}
        self.is_connected = False

    async def initialize(self):
        """Initialize the Solana client."""
        try:
            self.rpc_client = AsyncClient(
                settings.solana.rpc_url,
                commitment=Commitment(settings.solana.commitment),
            )

            # Test connection
            await self.rpc_client.get_health()
            self.is_connected = True

            self.logger.info("Solana client initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize Solana client: {e}")
            raise

    async def close(self):
        """Close the Solana client connections."""
        if self.ws_connection:
            await self.ws_connection.close()

        if self.rpc_client:
            await self.rpc_client.close()

        self.is_connected = False
        self.logger.info("Solana client closed")

    async def get_transaction(self, signature: str) -> Optional[Dict[str, Any]]:
        """Get transaction details by signature."""
        try:
            response = await retry_async(
                lambda: self.rpc_client.get_transaction(
                    Signature.from_string(signature),
                    encoding="jsonParsed",
                    max_supported_transaction_version=0,
                )
            )

            if response.value:
                return response.value.to_json()
            return None

        except Exception as e:
            self.logger.error(f"Error getting transaction {signature}: {e}")
            return None

    async def get_account_info(self, address: str) -> Optional[Dict[str, Any]]:
        """Get account information."""
        try:
            response = await retry_async(
                lambda: self.rpc_client.get_account_info(
                    PublicKey(address), encoding="jsonParsed"
                )
            )

            if response.value:
                return response.value.to_json()
            return None

        except Exception as e:
            self.logger.error(f"Error getting account info for {address}: {e}")
            return None

    async def get_token_accounts_by_owner(
        self, owner: str, mint: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get token accounts owned by an address."""
        try:
            if mint:
                response = await retry_async(
                    lambda: self.rpc_client.get_token_accounts_by_owner(
                        PublicKey(owner),
                        {"mint": PublicKey(mint)},
                        encoding="jsonParsed",
                    )
                )
            else:
                response = await retry_async(
                    lambda: self.rpc_client.get_token_accounts_by_owner(
                        PublicKey(owner),
                        {
                            "programId": PublicKey(
                                "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
                            )
                        },
                        encoding="jsonParsed",
                    )
                )

            if response.value:
                return [account.to_json() for account in response.value]
            return []

        except Exception as e:
            self.logger.error(f"Error getting token accounts for {owner}: {e}")
            return []

    async def get_signatures_for_address(
        self, address: str, limit: int = 100, before: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get transaction signatures for an address."""
        try:
            options = {"limit": limit}
            if before:
                options["before"] = Signature.from_string(before)

            response = await retry_async(
                lambda: self.rpc_client.get_signatures_for_address(
                    PublicKey(address), **options
                )
            )

            if response.value:
                return [sig.to_json() for sig in response.value]
            return []

        except Exception as e:
            self.logger.error(f"Error getting signatures for {address}: {e}")
            return []

    async def send_transaction(self, transaction: Transaction) -> Optional[str]:
        """Send a transaction to the network."""
        try:
            response = await retry_async(
                lambda: self.rpc_client.send_transaction(
                    transaction,
                    opts=TxOpts(
                        skip_preflight=False,
                        preflight_commitment=Commitment(settings.solana.commitment),
                    ),
                )
            )

            if response.value:
                signature = str(response.value)
                self.logger.info(f"Transaction sent successfully: {signature}")
                return signature

            return None

        except Exception as e:
            self.logger.error(f"Error sending transaction: {e}")
            return None

    async def confirm_transaction(self, signature: str, timeout: int = 60) -> bool:
        """Confirm a transaction."""
        try:
            response = await retry_async(
                lambda: self.rpc_client.confirm_transaction(
                    Signature.from_string(signature),
                    commitment=Commitment(settings.solana.commitment),
                )
            )

            return response.value[0].err is None

        except Exception as e:
            self.logger.error(f"Error confirming transaction {signature}: {e}")
            return False

    async def get_token_supply(self, mint: str) -> Optional[Dict[str, Any]]:
        """Get token supply information."""
        try:
            response = await retry_async(
                lambda: self.rpc_client.get_token_supply(PublicKey(mint))
            )

            if response.value:
                return response.value.to_json()
            return None

        except Exception as e:
            self.logger.error(f"Error getting token supply for {mint}: {e}")
            return None

    async def start_websocket_connection(self):
        """Start WebSocket connection for real-time updates."""
        try:
            self.ws_connection = await websockets.connect(
                settings.solana.ws_url, ping_interval=20, ping_timeout=10
            )

            self.logger.info("WebSocket connection established")

            # Start listening for messages
            asyncio.create_task(self._listen_websocket())

        except Exception as e:
            self.logger.error(f"Failed to establish WebSocket connection: {e}")
            raise

    async def _listen_websocket(self):
        """Listen for WebSocket messages."""
        try:
            async for message in self.ws_connection:
                try:
                    data = json.loads(message)
                    await self._handle_websocket_message(data)
                except json.JSONDecodeError:
                    self.logger.warning(f"Invalid JSON received: {message}")
                except Exception as e:
                    self.logger.error(f"Error handling WebSocket message: {e}")

        except websockets.exceptions.ConnectionClosed:
            self.logger.warning("WebSocket connection closed")
        except Exception as e:
            self.logger.error(f"WebSocket error: {e}")

    async def _handle_websocket_message(self, data: Dict[str, Any]):
        """Handle incoming WebSocket messages."""
        if "method" in data and data["method"] in self.subscription_callbacks:
            callback = self.subscription_callbacks[data["method"]]
            try:
                await callback(data.get("params", {}))
            except Exception as e:
                self.logger.error(f"Error in subscription callback: {e}")

    async def subscribe_to_logs(self, callback: Callable, mentions: List[str] = None):
        """Subscribe to transaction logs."""
        subscription_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "logsSubscribe",
            "params": [
                {"mentions": mentions} if mentions else "all",
                {"commitment": settings.solana.commitment},
            ],
        }

        self.subscription_callbacks["logsNotification"] = callback

        if self.ws_connection:
            await self.ws_connection.send(json.dumps(subscription_request))
            self.logger.info(f"Subscribed to logs with mentions: {mentions}")

    async def subscribe_to_account(self, account: str, callback: Callable):
        """Subscribe to account changes."""
        subscription_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "accountSubscribe",
            "params": [
                account,
                {"commitment": settings.solana.commitment, "encoding": "jsonParsed"},
            ],
        }

        self.subscription_callbacks["accountNotification"] = callback

        if self.ws_connection:
            await self.ws_connection.send(json.dumps(subscription_request))
            self.logger.info(f"Subscribed to account: {account}")


# Global Solana client instance
solana_client = SolanaClient()
