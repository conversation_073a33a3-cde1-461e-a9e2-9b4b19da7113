"""
Real-time transaction monitoring for the Solana Trading Bot.
"""

import asyncio
import re
from typing import Dict, Any, List, Optional, Set
from datetime import datetime
from dataclasses import dataclass

from src.blockchain.solana_client import solana_client
from src.config.settings import settings
from src.utils.logger import LoggerMixin
from src.utils.helpers import (
    extract_token_accounts_from_transaction,
    is_valid_solana_address,
)


@dataclass
class SwapTransaction:
    """Represents a DEX swap transaction."""

    signature: str
    wallet_address: str
    token_in: str
    token_out: str
    amount_in: float
    amount_out: float
    dex_program: str
    timestamp: datetime
    slot: int
    is_buy: bool  # True if buying token_out with SOL
    raw_data: Dict[str, Any]


class TransactionMonitor(LoggerMixin):
    """Monitors Solana transactions in real-time for DEX swaps."""

    def __init__(self):
        super().__init__()
        self.is_monitoring = False
        self.dex_programs = set(settings.monitoring.transaction_monitoring.dex_programs)
        self.transaction_callbacks = []
        self.processed_signatures = set()

        # SOL mint address (wrapped SOL)
        self.SOL_MINT = "So11111111111111111111111111111111111111112"

        # Pump.fun program ID
        self.PUMP_FUN_PROGRAM = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"

        # Add pump.fun to monitored programs
        self.dex_programs.add(self.PUMP_FUN_PROGRAM)

    async def start_monitoring(self):
        """Start monitoring transactions."""
        if self.is_monitoring:
            self.logger.warning("Transaction monitoring is already running")
            return

        try:
            # Initialize Solana client if not already done
            if not solana_client.is_connected:
                await solana_client.initialize()

            # Start WebSocket connection
            await solana_client.start_websocket_connection()

            # Subscribe to logs for DEX programs
            await solana_client.subscribe_to_logs(
                callback=self._handle_log_notification, mentions=list(self.dex_programs)
            )

            self.is_monitoring = True
            self.logger.info("Transaction monitoring started")

        except Exception as e:
            self.logger.error(f"Failed to start transaction monitoring: {e}")
            raise

    async def stop_monitoring(self):
        """Stop monitoring transactions."""
        self.is_monitoring = False
        await solana_client.close()
        self.logger.info("Transaction monitoring stopped")

    def add_transaction_callback(self, callback):
        """Add a callback function to be called when a swap transaction is detected."""
        self.transaction_callbacks.append(callback)

    async def _handle_log_notification(self, params: Dict[str, Any]):
        """Handle log notifications from WebSocket."""
        try:
            if "result" not in params:
                return

            result = params["result"]
            if "value" not in result:
                return

            log_data = result["value"]
            signature = log_data.get("signature")

            if not signature or signature in self.processed_signatures:
                return

            # Add to processed set to avoid duplicates
            self.processed_signatures.add(signature)

            # Clean up old signatures (keep last 10000)
            if len(self.processed_signatures) > 10000:
                # Remove oldest 1000 signatures
                signatures_list = list(self.processed_signatures)
                self.processed_signatures = set(signatures_list[-9000:])

            # Process the transaction
            await self._process_transaction(signature)

        except Exception as e:
            self.logger.error(f"Error handling log notification: {e}")

    async def _process_transaction(self, signature: str):
        """Process a transaction to extract swap information."""
        try:
            # Get full transaction details
            transaction_data = await solana_client.get_transaction(signature)

            if not transaction_data:
                return

            # Parse the transaction for swap information
            swap_transactions = await self._parse_swap_transaction(transaction_data)

            # Notify callbacks about each swap
            for swap in swap_transactions:
                await self._notify_callbacks(swap)

        except Exception as e:
            self.logger.error(f"Error processing transaction {signature}: {e}")

    async def _parse_swap_transaction(
        self, transaction_data: Dict[str, Any]
    ) -> List[SwapTransaction]:
        """Parse transaction data to extract swap information."""
        swaps = []

        try:
            # Extract basic transaction info
            signature = transaction_data.get("transaction", {}).get(
                "signatures", [None]
            )[0]
            slot = transaction_data.get("slot", 0)
            block_time = transaction_data.get("blockTime")

            if not signature or not block_time:
                return swaps

            timestamp = datetime.fromtimestamp(block_time)

            # Get transaction meta
            meta = transaction_data.get("meta", {})
            if meta.get("err"):
                return swaps  # Skip failed transactions

            # Extract account keys
            message = transaction_data.get("transaction", {}).get("message", {})
            account_keys = message.get("accountKeys", [])

            # Get pre and post token balances
            pre_balances = meta.get("preTokenBalances", [])
            post_balances = meta.get("postTokenBalances", [])

            # Find token balance changes
            balance_changes = self._calculate_balance_changes(
                pre_balances, post_balances
            )

            # Group balance changes by owner (wallet)
            wallet_changes = {}
            for change in balance_changes:
                owner = change.get("owner")
                if owner and is_valid_solana_address(owner):
                    if owner not in wallet_changes:
                        wallet_changes[owner] = []
                    wallet_changes[owner].append(change)

            # Identify swaps from balance changes
            for wallet_address, changes in wallet_changes.items():
                swap = self._identify_swap_from_changes(
                    wallet_address,
                    changes,
                    signature,
                    timestamp,
                    slot,
                    transaction_data,
                )
                if swap:
                    swaps.append(swap)

        except Exception as e:
            self.logger.error(f"Error parsing swap transaction: {e}")

        return swaps

    def _calculate_balance_changes(
        self, pre_balances: List[Dict], post_balances: List[Dict]
    ) -> List[Dict[str, Any]]:
        """Calculate token balance changes from pre and post balances."""
        changes = []

        # Create lookup dictionaries
        pre_lookup = {}
        for balance in pre_balances:
            key = (balance.get("accountIndex"), balance.get("mint"))
            pre_lookup[key] = balance

        post_lookup = {}
        for balance in post_balances:
            key = (balance.get("accountIndex"), balance.get("mint"))
            post_lookup[key] = balance

        # Find all unique account/mint combinations
        all_keys = set(pre_lookup.keys()) | set(post_lookup.keys())

        for key in all_keys:
            account_index, mint = key

            pre_balance = pre_lookup.get(key, {})
            post_balance = post_lookup.get(key, {})

            pre_amount = float(pre_balance.get("uiTokenAmount", {}).get("uiAmount", 0))
            post_amount = float(
                post_balance.get("uiTokenAmount", {}).get("uiAmount", 0)
            )

            change = post_amount - pre_amount

            if abs(change) > 0.000001:  # Ignore dust changes
                changes.append(
                    {
                        "accountIndex": account_index,
                        "mint": mint,
                        "owner": post_balance.get("owner") or pre_balance.get("owner"),
                        "change": change,
                        "pre_amount": pre_amount,
                        "post_amount": post_amount,
                    }
                )

        return changes

    def _identify_swap_from_changes(
        self,
        wallet_address: str,
        changes: List[Dict[str, Any]],
        signature: str,
        timestamp: datetime,
        slot: int,
        raw_data: Dict[str, Any],
    ) -> Optional[SwapTransaction]:
        """Identify if balance changes represent a swap transaction."""

        if len(changes) < 2:
            return None

        # Find tokens that increased and decreased
        increases = [c for c in changes if c["change"] > 0]
        decreases = [c for c in changes if c["change"] < 0]

        if not increases or not decreases:
            return None

        # For simplicity, take the largest increase and decrease
        # In practice, you might want more sophisticated logic
        largest_increase = max(increases, key=lambda x: abs(x["change"]))
        largest_decrease = max(decreases, key=lambda x: abs(x["change"]))

        token_in = largest_decrease["mint"]
        token_out = largest_increase["mint"]
        amount_in = abs(largest_decrease["change"])
        amount_out = largest_increase["change"]

        # Determine if this is a buy (SOL -> token) or sell (token -> SOL)
        is_buy = token_in == self.SOL_MINT

        # Try to identify the DEX program
        dex_program = self._identify_dex_program(raw_data)

        return SwapTransaction(
            signature=signature,
            wallet_address=wallet_address,
            token_in=token_in,
            token_out=token_out,
            amount_in=amount_in,
            amount_out=amount_out,
            dex_program=dex_program,
            timestamp=timestamp,
            slot=slot,
            is_buy=is_buy,
            raw_data=raw_data,
        )

    def _identify_dex_program(self, transaction_data: Dict[str, Any]) -> str:
        """Identify which DEX program was used in the transaction."""
        try:
            # Check instructions for known DEX program IDs
            message = transaction_data.get("transaction", {}).get("message", {})
            instructions = message.get("instructions", [])

            for instruction in instructions:
                program_id = instruction.get("programId")
                if program_id in self.dex_programs:
                    return program_id

            # Check account keys for DEX programs
            account_keys = message.get("accountKeys", [])
            for account in account_keys:
                if isinstance(account, dict):
                    pubkey = account.get("pubkey")
                elif isinstance(account, str):
                    pubkey = account
                else:
                    continue

                if pubkey in self.dex_programs:
                    return pubkey

        except Exception as e:
            self.logger.error(f"Error identifying DEX program: {e}")

        return "unknown"

    async def _notify_callbacks(self, swap: SwapTransaction):
        """Notify all registered callbacks about a new swap transaction."""
        for callback in self.transaction_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(swap)
                else:
                    callback(swap)
            except Exception as e:
                self.logger.error(f"Error in transaction callback: {e}")


# Global transaction monitor instance
transaction_monitor = TransactionMonitor()
