"""
Database management for the Solana Trading Bot.
"""

import asyncio
from typing import Optional, List, Dict, Any
from sqlalchemy import create_engine, select, and_, or_, desc, func
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker, Session
from contextlib import asynccontextmanager, contextmanager
from datetime import datetime, timedelta

from src.config.settings import settings
from src.data.models import Base, Wallet, Token, Transaction, Trade, CopyTrade, PerformanceMetric, BotState
from src.utils.logger import get_logger

logger = get_logger(__name__)


class DatabaseManager:
    """Manages database connections and operations."""
    
    def __init__(self):
        self.engine = None
        self.async_engine = None
        self.session_factory = None
        self.async_session_factory = None
        
    async def initialize(self):
        """Initialize database connections and create tables."""
        try:
            # Create sync engine for migrations
            self.engine = create_engine(
                settings.database.url,
                echo=settings.database.echo
            )
            
            # Create async engine for operations
            async_url = settings.database.url.replace('sqlite:///', 'sqlite+aiosqlite:///')
            self.async_engine = create_async_engine(
                async_url,
                echo=settings.database.echo
            )
            
            # Create session factories
            self.session_factory = sessionmaker(bind=self.engine)
            self.async_session_factory = async_sessionmaker(
                bind=self.async_engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            # Create tables
            Base.metadata.create_all(self.engine)
            
            logger.info("Database initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    @contextmanager
    def get_session(self):
        """Get a synchronous database session."""
        session = self.session_factory()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    @asynccontextmanager
    async def get_async_session(self):
        """Get an asynchronous database session."""
        async with self.async_session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
    
    async def close(self):
        """Close database connections."""
        if self.async_engine:
            await self.async_engine.dispose()
        if self.engine:
            self.engine.dispose()


class WalletRepository:
    """Repository for wallet-related database operations."""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    async def create_or_update_wallet(self, address: str, **kwargs) -> Wallet:
        """Create a new wallet or update existing one."""
        async with self.db.get_async_session() as session:
            # Check if wallet exists
            result = await session.execute(
                select(Wallet).where(Wallet.address == address)
            )
            wallet = result.scalar_one_or_none()
            
            if wallet:
                # Update existing wallet
                for key, value in kwargs.items():
                    setattr(wallet, key, value)
                wallet.last_updated = datetime.utcnow()
            else:
                # Create new wallet
                wallet = Wallet(address=address, **kwargs)
                session.add(wallet)
            
            await session.commit()
            await session.refresh(wallet)
            return wallet
    
    async def get_wallet_by_address(self, address: str) -> Optional[Wallet]:
        """Get wallet by address."""
        async with self.db.get_async_session() as session:
            result = await session.execute(
                select(Wallet).where(Wallet.address == address)
            )
            return result.scalar_one_or_none()
    
    async def get_active_wallets(self, trader_type: Optional[str] = None) -> List[Wallet]:
        """Get all active wallets, optionally filtered by trader type."""
        async with self.db.get_async_session() as session:
            query = select(Wallet).where(Wallet.is_active == True)
            
            if trader_type:
                query = query.where(Wallet.trader_type == trader_type)
            
            result = await session.execute(query)
            return result.scalars().all()
    
    async def get_copy_trading_wallets(self) -> List[Wallet]:
        """Get wallets that are being copy traded."""
        async with self.db.get_async_session() as session:
            result = await session.execute(
                select(Wallet).where(
                    and_(Wallet.is_active == True, Wallet.is_copy_trading == True)
                )
            )
            return result.scalars().all()
    
    async def get_top_performers(self, limit: int = 50, min_trades: int = 5) -> List[Wallet]:
        """Get top performing wallets by ROI."""
        async with self.db.get_async_session() as session:
            result = await session.execute(
                select(Wallet)
                .where(
                    and_(
                        Wallet.is_active == True,
                        Wallet.total_trades >= min_trades
                    )
                )
                .order_by(desc(Wallet.total_roi))
                .limit(limit)
            )
            return result.scalars().all()


class TransactionRepository:
    """Repository for transaction-related database operations."""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    async def create_transaction(self, **kwargs) -> Transaction:
        """Create a new transaction."""
        async with self.db.get_async_session() as session:
            transaction = Transaction(**kwargs)
            session.add(transaction)
            await session.commit()
            await session.refresh(transaction)
            return transaction
    
    async def get_wallet_transactions(
        self, 
        wallet_id: int, 
        limit: int = 100,
        token_id: Optional[int] = None
    ) -> List[Transaction]:
        """Get transactions for a wallet."""
        async with self.db.get_async_session() as session:
            query = select(Transaction).where(Transaction.wallet_id == wallet_id)
            
            if token_id:
                query = query.where(Transaction.token_id == token_id)
            
            query = query.order_by(desc(Transaction.block_time)).limit(limit)
            
            result = await session.execute(query)
            return result.scalars().all()
    
    async def get_recent_transactions(self, hours: int = 24) -> List[Transaction]:
        """Get recent transactions within specified hours."""
        async with self.db.get_async_session() as session:
            cutoff_time = datetime.utcnow() - timedelta(hours=hours)
            
            result = await session.execute(
                select(Transaction)
                .where(Transaction.block_time >= cutoff_time)
                .order_by(desc(Transaction.block_time))
            )
            return result.scalars().all()


class TradeRepository:
    """Repository for trade-related database operations."""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    async def create_trade(self, **kwargs) -> Trade:
        """Create a new trade."""
        async with self.db.get_async_session() as session:
            trade = Trade(**kwargs)
            session.add(trade)
            await session.commit()
            await session.refresh(trade)
            return trade
    
    async def update_trade(self, trade_id: int, **kwargs) -> Optional[Trade]:
        """Update an existing trade."""
        async with self.db.get_async_session() as session:
            result = await session.execute(
                select(Trade).where(Trade.id == trade_id)
            )
            trade = result.scalar_one_or_none()
            
            if trade:
                for key, value in kwargs.items():
                    setattr(trade, key, value)
                trade.updated_at = datetime.utcnow()
                await session.commit()
                await session.refresh(trade)
            
            return trade
    
    async def get_wallet_trades(self, wallet_id: int, completed_only: bool = True) -> List[Trade]:
        """Get trades for a wallet."""
        async with self.db.get_async_session() as session:
            query = select(Trade).where(Trade.wallet_id == wallet_id)
            
            if completed_only:
                query = query.where(Trade.is_complete == True)
            
            query = query.order_by(desc(Trade.entry_time))
            
            result = await session.execute(query)
            return result.scalars().all()
    
    async def get_profitable_trades(self, min_roi: float = 10.0) -> List[Trade]:
        """Get trades with ROI above threshold."""
        async with self.db.get_async_session() as session:
            result = await session.execute(
                select(Trade)
                .where(
                    and_(
                        Trade.is_complete == True,
                        Trade.roi_percentage >= min_roi
                    )
                )
                .order_by(desc(Trade.roi_percentage))
            )
            return result.scalars().all()


class CopyTradeRepository:
    """Repository for copy trade operations."""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    async def create_copy_trade(self, **kwargs) -> CopyTrade:
        """Create a new copy trade."""
        async with self.db.get_async_session() as session:
            copy_trade = CopyTrade(**kwargs)
            session.add(copy_trade)
            await session.commit()
            await session.refresh(copy_trade)
            return copy_trade
    
    async def update_copy_trade_status(
        self, 
        copy_trade_id: int, 
        status: str, 
        **kwargs
    ) -> Optional[CopyTrade]:
        """Update copy trade status and details."""
        async with self.db.get_async_session() as session:
            result = await session.execute(
                select(CopyTrade).where(CopyTrade.id == copy_trade_id)
            )
            copy_trade = result.scalar_one_or_none()
            
            if copy_trade:
                copy_trade.status = status
                for key, value in kwargs.items():
                    setattr(copy_trade, key, value)
                copy_trade.updated_at = datetime.utcnow()
                await session.commit()
                await session.refresh(copy_trade)
            
            return copy_trade
    
    async def get_pending_copy_trades(self) -> List[CopyTrade]:
        """Get pending copy trades."""
        async with self.db.get_async_session() as session:
            result = await session.execute(
                select(CopyTrade).where(CopyTrade.status == 'pending')
            )
            return result.scalars().all()


# Global database manager instance
db_manager = DatabaseManager()
