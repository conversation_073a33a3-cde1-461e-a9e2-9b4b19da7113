"""
Bloom Telegram Bot for Solana Trading Bot integration.
Provides real-time notifications and trading functionality through Telegram.
"""

import asyncio
import json
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, BotCommand
from telegram.ext import (
    Application,
    CommandHandler,
    CallbackQueryHandler,
    MessageHandler,
    filters,
    ContextTypes,
)

from src.config.settings import settings
from src.utils.logger import LoggerMixin
from src.utils.helpers import format_sol_amount, format_percentage, get_current_datetime


class BloomBot(LoggerMixin):
    """Bloom Telegram Bot for trading notifications and controls."""

    def __init__(self, token: str):
        super().__init__()
        self.token = token
        self.application = None
        self.authorized_users = set()
        self.notification_callbacks = []
        self.is_running = False

        # Trading state
        self.trading_enabled = True
        self.notifications_enabled = True

        # Subscription management
        self.subscribers = {
            "all": set(),
            "trades": set(),
            "snipers": set(),
            "pump_launches": set(),
            "alerts": set(),
        }

    async def initialize(self):
        """Initialize the Telegram bot."""
        try:
            self.application = Application.builder().token(self.token).build()

            # Register command handlers
            self.application.add_handler(CommandHandler("start", self.start_command))
            self.application.add_handler(CommandHandler("help", self.help_command))
            self.application.add_handler(CommandHandler("status", self.status_command))
            self.application.add_handler(CommandHandler("wallet", self.wallet_command))
            self.application.add_handler(
                CommandHandler("balance", self.balance_command)
            )
            self.application.add_handler(
                CommandHandler("transactions", self.transactions_command)
            )
            self.application.add_handler(
                CommandHandler("subscribe", self.subscribe_command)
            )
            self.application.add_handler(
                CommandHandler("unsubscribe", self.unsubscribe_command)
            )
            self.application.add_handler(
                CommandHandler("wallets", self.wallets_command)
            )
            self.application.add_handler(CommandHandler("trades", self.trades_command))
            self.application.add_handler(CommandHandler("pump", self.pump_command))
            self.application.add_handler(
                CommandHandler("settings", self.settings_command)
            )
            self.application.add_handler(
                CommandHandler("emergency", self.emergency_command)
            )

            # Register callback query handler for inline keyboards
            self.application.add_handler(CallbackQueryHandler(self.button_callback))

            # Register message handler for text messages
            self.application.add_handler(
                MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message)
            )

            # Set bot commands
            await self.set_bot_commands()

            self.logger.info("Bloom Telegram bot initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize Telegram bot: {e}")
            raise

    async def start(self):
        """Start the Telegram bot."""
        try:
            if self.is_running:
                self.logger.warning("Telegram bot is already running")
                return

            await self.application.initialize()
            await self.application.start()
            await self.application.updater.start_polling()

            self.is_running = True
            self.logger.info("Bloom Telegram bot started successfully")

        except Exception as e:
            self.logger.error(f"Failed to start Telegram bot: {e}")
            raise

    async def stop(self):
        """Stop the Telegram bot."""
        try:
            if not self.is_running:
                return

            await self.application.updater.stop()
            await self.application.stop()
            await self.application.shutdown()

            self.is_running = False
            self.logger.info("Bloom Telegram bot stopped")

        except Exception as e:
            self.logger.error(f"Error stopping Telegram bot: {e}")

    async def set_bot_commands(self):
        """Set bot commands for the Telegram menu."""
        commands = [
            BotCommand(
                "start", "🌸 Welcome to BloomSolana - Your Solana trading intelligence"
            ),
            BotCommand("help", "📚 Complete command guide & pro features"),
            BotCommand("status", "📊 Real-time bot performance & trading metrics"),
            BotCommand("wallet", "💼 Your wallet balance & transaction history"),
            BotCommand("balance", "💰 Current SOL & token balances"),
            BotCommand("transactions", "📋 Recent wallet transactions"),
            BotCommand("subscribe", "🔔 Alpha notifications & trading alerts"),
            BotCommand("unsubscribe", "🔕 Manage notification preferences"),
            BotCommand("wallets", "🎯 Elite trader watchlist & sniper analytics"),
            BotCommand("trades", "💰 Live copy trading activity & results"),
            BotCommand("pump", "🚀 Pump.fun market intelligence & launches"),
            BotCommand("settings", "⚙️ Advanced bot configuration & risk management"),
            BotCommand("emergency", "🚨 Emergency stop all trading activities"),
        ]

        await self.application.bot.set_my_commands(commands)

    def add_authorized_user(self, user_id: int):
        """Add an authorized user."""
        self.authorized_users.add(user_id)
        self.logger.info(f"Added authorized user: {user_id}")

    def is_authorized(self, user_id: int) -> bool:
        """Check if user is authorized."""
        return user_id in self.authorized_users

    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command."""
        user_id = update.effective_user.id

        if not self.is_authorized(user_id):
            await update.message.reply_text(
                "🚫 Unauthorized access. Contact the bot administrator."
            )
            return

        welcome_text = f"""
🌸 **Welcome to @BloomSolana_bot, {update.effective_user.first_name}!**

Your premium Solana trading intelligence platform 🚀

🎯 **Elite Wallet Tracking**
• Monitor top snipers, insiders & whales
• Copy 10x+ winning strategies
• Real-time alpha alerts

💎 **Pump.fun Mastery**
• Instant new token detection
• Early entry opportunities
• Sniper activity monitoring

⚡ **Advanced Trading Suite**
• Smart copy trading with stop-loss
• Risk management protocols
• Performance analytics dashboard
• Lightning-fast notifications

🔥 **Quick Actions:**
/status - Bot performance metrics
/wallets - Elite trader watchlist
/trades - Live trading activity
/pump - Pump.fun statistics
/subscribe - Alpha notifications

**Ready to bloom your portfolio? 🌸💰**
        """

        keyboard = [
            [
                InlineKeyboardButton("📊 Status", callback_data="status"),
                InlineKeyboardButton("🔔 Subscribe", callback_data="subscribe"),
            ],
            [
                InlineKeyboardButton("👥 Wallets", callback_data="wallets"),
                InlineKeyboardButton("🚀 Pump.fun", callback_data="pump"),
            ],
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(
            welcome_text, reply_markup=reply_markup, parse_mode="Markdown"
        )

    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command."""
        if not self.is_authorized(update.effective_user.id):
            return

        help_text = """
🌸 **@BloomSolana_bot Commands**

**📊 Monitoring:**
/status - Bot status and performance
/wallets - Show tracked profitable wallets
/trades - Recent copy trades
/pump - Pump.fun statistics and launches

**🔔 Notifications:**
/subscribe [type] - Subscribe to notifications
  • all - All notifications
  • trades - Copy trade alerts
  • snipers - New sniper discoveries
  • pump_launches - New pump.fun tokens
  • alerts - Important alerts only

/unsubscribe [type] - Unsubscribe from notifications

**⚙️ Controls:**
/settings - Bot settings and controls
/emergency - Emergency stop all trading

**💡 Tips:**
• Use inline buttons for quick actions
• Subscribe to get real-time alerts
• Check /pump for latest launches
• Monitor /status for performance
        """

        await update.message.reply_text(help_text, parse_mode="Markdown")

    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /status command."""
        if not self.is_authorized(update.effective_user.id):
            return

        try:
            # Get bot status (this would integrate with the main bot)
            status_text = await self.get_status_message()

            keyboard = [
                [
                    InlineKeyboardButton("🔄 Refresh", callback_data="status"),
                    InlineKeyboardButton("⚙️ Settings", callback_data="settings"),
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                status_text, reply_markup=reply_markup, parse_mode="Markdown"
            )

        except Exception as e:
            await update.message.reply_text(f"❌ Error getting status: {e}")

    async def subscribe_command(
        self, update: Update, context: ContextTypes.DEFAULT_TYPE
    ):
        """Handle /subscribe command."""
        if not self.is_authorized(update.effective_user.id):
            return

        user_id = update.effective_user.id
        args = context.args

        if not args:
            # Show subscription options
            keyboard = [
                [
                    InlineKeyboardButton(
                        "🔔 All Notifications", callback_data="sub_all"
                    ),
                    InlineKeyboardButton("💰 Trades Only", callback_data="sub_trades"),
                ],
                [
                    InlineKeyboardButton("🎯 Snipers", callback_data="sub_snipers"),
                    InlineKeyboardButton(
                        "🚀 Pump Launches", callback_data="sub_pump_launches"
                    ),
                ],
                [InlineKeyboardButton("⚠️ Alerts Only", callback_data="sub_alerts")],
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                "🔔 **Choose Notification Type:**",
                reply_markup=reply_markup,
                parse_mode="Markdown",
            )
            return

        subscription_type = args[0].lower()
        if subscription_type in self.subscribers:
            self.subscribers[subscription_type].add(user_id)
            await update.message.reply_text(
                f"✅ Subscribed to {subscription_type} notifications!"
            )
        else:
            await update.message.reply_text(
                "❌ Invalid subscription type. Use: all, trades, snipers, pump_launches, alerts"
            )

    async def unsubscribe_command(
        self, update: Update, context: ContextTypes.DEFAULT_TYPE
    ):
        """Handle /unsubscribe command."""
        if not self.is_authorized(update.effective_user.id):
            return

        user_id = update.effective_user.id
        args = context.args

        if not args:
            # Show unsubscribe options
            keyboard = [
                [
                    InlineKeyboardButton(
                        "🔕 All Notifications", callback_data="unsub_all"
                    ),
                    InlineKeyboardButton(
                        "💰 Trades Only", callback_data="unsub_trades"
                    ),
                ],
                [
                    InlineKeyboardButton("🎯 Snipers", callback_data="unsub_snipers"),
                    InlineKeyboardButton(
                        "🚀 Pump Launches", callback_data="unsub_pump_launches"
                    ),
                ],
                [InlineKeyboardButton("⚠️ Alerts Only", callback_data="unsub_alerts")],
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                "🔕 **Choose Notification Type to Unsubscribe:**",
                reply_markup=reply_markup,
                parse_mode="Markdown",
            )
            return

        subscription_type = args[0].lower()
        if subscription_type in self.subscribers:
            self.subscribers[subscription_type].discard(user_id)
            await update.message.reply_text(
                f"✅ Unsubscribed from {subscription_type} notifications!"
            )
        else:
            await update.message.reply_text(
                "❌ Invalid subscription type. Use: all, trades, snipers, pump_launches, alerts"
            )

    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle inline keyboard button callbacks."""
        query = update.callback_query
        await query.answer()

        if not self.is_authorized(query.from_user.id):
            return

        data = query.data

        if data == "status":
            status_text = await self.get_status_message()
            await query.edit_message_text(status_text, parse_mode="Markdown")

        elif data.startswith("sub_"):
            subscription_type = data[4:]  # Remove "sub_" prefix
            user_id = query.from_user.id
            self.subscribers[subscription_type].add(user_id)
            await query.edit_message_text(
                f"✅ Subscribed to {subscription_type} notifications!"
            )

        elif data == "emergency_stop":
            await self.handle_emergency_stop(query)

        elif data == "toggle_trading":
            self.trading_enabled = not self.trading_enabled
            status = "enabled" if self.trading_enabled else "disabled"
            await query.edit_message_text(f"🔄 Trading {status}")

    async def wallets_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /wallets command."""
        if not self.is_authorized(update.effective_user.id):
            return

        # This would integrate with the wallet tracker
        wallets_text = """
🎯 **Top Tracked Wallets**

**🔥 Snipers:**
• `DnP1YHAe...` - 85% win rate, 12.5x avg ROI
• `FSxwKvXC...` - 78% win rate, 8.2x avg ROI
• `HwUedgcj...` - 92% win rate, 15.1x avg ROI

**🐋 Whales:**
• `3KJWMTwq...` - 2.5M SOL volume
• `GZVSEAaj...` - 1.8M SOL volume

**📊 Performance (24h):**
• Total tracked: 45 wallets
• Active traders: 12
• Copy trades executed: 8
        """

        await update.message.reply_text(wallets_text, parse_mode="Markdown")

    async def trades_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /trades command."""
        if not self.is_authorized(update.effective_user.id):
            return

        # This would integrate with the copy trader
        trades_text = """
💰 **Recent Copy Trades**

**✅ Successful:**
• BIOCOMPUTE - +2.5x ROI (2.3 SOL)
• GOATSEUS - +1.8x ROI (1.5 SOL)
• Truth - +3.2x ROI (0.8 SOL)

**⏳ Active:**
• VEO3 - Entry: 4.95 SOL
• FreeWill - Entry: 2.8 SOL

**📊 Summary (24h):**
• Total trades: 12
• Success rate: 75%
• Total P&L: ***** SOL
        """

        await update.message.reply_text(trades_text, parse_mode="Markdown")

    async def pump_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /pump command."""
        if not self.is_authorized(update.effective_user.id):
            return

        # This would integrate with the pump.fun monitor
        pump_text = """
🚀 **Pump.fun Statistics**

**📊 Today's Activity:**
• New launches: 23 tokens
• Snipes detected: 12
• Early trades: 45
• Top ROI: 15.6x (BIOCOMPUTE)

**🔥 Recent Launches:**
• BIOCOMPUTE - 2.5 SOL MC
• GOATSEUS - 1.8 SOL MC
• Truth - 3.2 SOL MC
• VEO3 - 4.9 SOL MC

**⚡ Live Monitoring:**
• WebSocket: 🟢 Connected
• Tokens tracked: 156
• Avg detection speed: 0.8s
        """

        await update.message.reply_text(pump_text, parse_mode="Markdown")

    async def settings_command(
        self, update: Update, context: ContextTypes.DEFAULT_TYPE
    ):
        """Handle /settings command."""
        if not self.is_authorized(update.effective_user.id):
            return

        keyboard = [
            [
                InlineKeyboardButton(
                    "🔄 Toggle Trading", callback_data="toggle_trading"
                ),
                InlineKeyboardButton("🔔 Notifications", callback_data="notifications"),
            ],
            [
                InlineKeyboardButton("⚠️ Risk Settings", callback_data="risk_settings"),
                InlineKeyboardButton(
                    "🚨 Emergency Stop", callback_data="emergency_stop"
                ),
            ],
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        settings_text = f"""
⚙️ **Bot Settings**

**🤖 Status:**
• Trading: {'🟢 Enabled' if self.trading_enabled else '🔴 Disabled'}
• Notifications: {'🟢 On' if self.notifications_enabled else '🔴 Off'}

**📊 Current Config:**
• Max position size: 1.0 SOL
• Stop loss: -20%
• Take profit: +50%
• Copy trading: Enabled

Use the buttons below to modify settings.
        """

        await update.message.reply_text(
            settings_text, reply_markup=reply_markup, parse_mode="Markdown"
        )

    async def emergency_command(
        self, update: Update, context: ContextTypes.DEFAULT_TYPE
    ):
        """Handle /emergency command."""
        if not self.is_authorized(update.effective_user.id):
            return

        keyboard = [
            [
                InlineKeyboardButton(
                    "🚨 CONFIRM EMERGENCY STOP", callback_data="emergency_stop"
                ),
                InlineKeyboardButton("❌ Cancel", callback_data="cancel"),
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(
            "🚨 **EMERGENCY STOP**\n\n"
            "This will immediately halt all trading activities.\n"
            "Are you sure you want to proceed?",
            reply_markup=reply_markup,
            parse_mode="Markdown",
        )

    async def wallet_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /wallet command - show wallet overview."""
        if not self.is_authorized(update.effective_user.id):
            return

        try:
            # Get wallet status from user wallet monitor
            wallet_status = await self.get_wallet_status()

            wallet_text = f"""
💼 **Your @BloomSolana_bot Wallet**

**📍 Address:** `{wallet_status.get('address', 'Not configured')[:8]}...`
**💰 SOL Balance:** {format_sol_amount(wallet_status.get('sol_balance', 0))}
**🪙 Token Holdings:** {wallet_status.get('token_count', 0)} tokens
**💎 Total Value:** {format_sol_amount(wallet_status.get('total_value_sol', 0))}

**📊 Monitoring Status:**
• Balance Alerts: {'🟢 Active' if wallet_status.get('monitoring_enabled') else '🔴 Inactive'}
• Last Updated: {wallet_status.get('last_updated', 'Never')[:19] if wallet_status.get('last_updated') else 'Never'}

**🔥 Quick Actions:**
/balance - Detailed balance breakdown
/transactions - Recent activity
            """

            keyboard = [
                [
                    InlineKeyboardButton("💰 Balance", callback_data="wallet_balance"),
                    InlineKeyboardButton(
                        "📋 Transactions", callback_data="wallet_transactions"
                    ),
                ],
                [
                    InlineKeyboardButton("🔄 Refresh", callback_data="wallet_refresh"),
                    InlineKeyboardButton("⚙️ Settings", callback_data="wallet_settings"),
                ],
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                wallet_text, reply_markup=reply_markup, parse_mode="Markdown"
            )

        except Exception as e:
            await update.message.reply_text(f"❌ Error getting wallet info: {e}")

    async def balance_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /balance command - show detailed balance."""
        if not self.is_authorized(update.effective_user.id):
            return

        try:
            wallet_status = await self.get_wallet_status()

            balance_text = f"""
💰 **@BloomSolana_bot Balance Details**

**💎 SOL Balance:**
• Available: {format_sol_amount(wallet_status.get('sol_balance', 0))}
• USD Value: ~${wallet_status.get('sol_balance', 0) * 100:.2f} (est.)

**🪙 Token Holdings:**
• Total Tokens: {wallet_status.get('token_count', 0)}
• Estimated Value: {format_sol_amount(wallet_status.get('total_value_sol', 0))}

**📊 Portfolio Breakdown:**
• SOL: {wallet_status.get('sol_balance', 0):.4f} SOL
• Tokens: {format_sol_amount(wallet_status.get('total_value_sol', 0) - wallet_status.get('sol_balance', 0))}

**🔄 Last Updated:** {wallet_status.get('last_updated', 'Never')[:19] if wallet_status.get('last_updated') else 'Never'}
            """

            await update.message.reply_text(balance_text, parse_mode="Markdown")

        except Exception as e:
            await update.message.reply_text(f"❌ Error getting balance: {e}")

    async def transactions_command(
        self, update: Update, context: ContextTypes.DEFAULT_TYPE
    ):
        """Handle /transactions command - show recent transactions."""
        if not self.is_authorized(update.effective_user.id):
            return

        try:
            recent_activity = await self.get_recent_wallet_activity()

            if not recent_activity:
                await update.message.reply_text("📋 No recent transactions found.")
                return

            transactions_text = "📋 **Recent Wallet Transactions**\n\n"

            for i, tx in enumerate(recent_activity[:10], 1):
                tx_type = tx.get("type", "unknown").upper()
                amount = format_sol_amount(tx.get("amount_sol", 0))
                timestamp = (
                    tx.get("timestamp", "")[:16] if tx.get("timestamp") else "Unknown"
                )
                signature = (
                    tx.get("signature", "")[:8] if tx.get("signature") else "Unknown"
                )

                transactions_text += f"""
**{i}.** {tx_type} - {amount}
• Time: {timestamp}
• Tx: `{signature}...`
                """

            transactions_text += (
                f"\n💡 Showing last {len(recent_activity[:10])} transactions"
            )

            await update.message.reply_text(transactions_text, parse_mode="Markdown")

        except Exception as e:
            await update.message.reply_text(f"❌ Error getting transactions: {e}")

    async def get_wallet_status(self) -> Dict[str, Any]:
        """Get wallet status from user wallet monitor."""
        # This will be connected to the actual wallet monitor
        if hasattr(self, "wallet_monitor") and self.wallet_monitor:
            return await self.wallet_monitor.get_wallet_status()
        else:
            # Return mock data for now
            return {
                "address": "41g5yba92CD4VgSv5XSYHkgGW4hwtdKQ6c7SYHWBBJKX",
                "sol_balance": 0.0,
                "token_count": 0,
                "total_value_sol": 0.0,
                "monitoring_enabled": True,
                "last_updated": get_current_datetime().isoformat(),
            }

    async def get_recent_wallet_activity(self) -> List[Dict[str, Any]]:
        """Get recent wallet activity."""
        # This will be connected to the actual wallet monitor
        if hasattr(self, "wallet_monitor") and self.wallet_monitor:
            return await self.wallet_monitor.get_recent_activity()
        else:
            # Return mock data for now
            return []

    def set_wallet_monitor(self, wallet_monitor):
        """Set the wallet monitor instance."""
        self.wallet_monitor = wallet_monitor

    async def get_status_message(self) -> str:
        """Generate status message."""
        try:
            # This would integrate with the main bot to get real status
            # For now, return a template

            current_time = get_current_datetime().strftime("%H:%M:%S UTC")

            status_text = f"""
🌸 **@BloomSolana_bot Status** - {current_time}

🤖 **System Health:**
• Status: {'🟢 Online' if self.is_running else '🔴 Offline'}
• Trading: {'🟢 Active' if self.trading_enabled else '🔴 Paused'}
• Notifications: {'🟢 Live' if self.notifications_enabled else '🔴 Muted'}

💰 **Trading Performance (24h):**
• Copy Trades Executed: 12
• Success Rate: 75%
• Total P&L: ***** SOL
• Active Positions: 3

🎯 **Intelligence Network:**
• Elite Wallets Tracked: 45
• Snipers Identified: 8
• Pump Launches Monitored: 23
• Alpha Subscribers: {len(self.subscribers['all'])}

🚀 **Pump.fun Intelligence:**
• New Tokens (1h): 5
• Sniper Activity: 12 detected
• Best ROI Today: 15.6x

**Ready to bloom your portfolio? 🌸💰**
            """

            return status_text

        except Exception as e:
            return f"❌ Error getting status: {e}"

    async def handle_emergency_stop(self, query):
        """Handle emergency stop."""
        try:
            # This would integrate with the main bot's emergency stop
            self.trading_enabled = False

            await query.edit_message_text(
                "🚨 **EMERGENCY STOP ACTIVATED**\n\n"
                "• All trading halted\n"
                "• Positions being closed\n"
                "• Risk management active\n\n"
                "Use /settings to re-enable trading."
            )

            # Notify all subscribers
            await self.broadcast_message(
                "🚨 **EMERGENCY STOP**\nAll trading has been halted by user command.",
                "alerts",
            )

        except Exception as e:
            await query.edit_message_text(f"❌ Error during emergency stop: {e}")

    async def broadcast_message(self, message: str, subscription_type: str = "all"):
        """Broadcast message to subscribers."""
        try:
            subscribers = self.subscribers.get(subscription_type, set())

            for user_id in subscribers:
                try:
                    await self.application.bot.send_message(
                        chat_id=user_id, text=message, parse_mode="Markdown"
                    )
                except Exception as e:
                    self.logger.error(f"Failed to send message to {user_id}: {e}")

        except Exception as e:
            self.logger.error(f"Error broadcasting message: {e}")

    async def send_trade_notification(self, trade_data: Dict[str, Any]):
        """Send trade notification to subscribers."""
        try:
            message = f"""
💰 **Copy Trade Executed**

🎯 **Source:** {trade_data.get('source_wallet', 'Unknown')[:8]}...
🪙 **Token:** {trade_data.get('token_symbol', 'Unknown')}
📊 **Type:** {trade_data.get('trade_type', 'Unknown').upper()}
💵 **Amount:** {format_sol_amount(trade_data.get('amount_sol', 0))}
⚡ **Speed:** {trade_data.get('delay_seconds', 0):.1f}s delay

🔗 **Signature:** `{trade_data.get('signature', 'Unknown')[:16]}...`
            """

            await self.broadcast_message(message, "trades")

        except Exception as e:
            self.logger.error(f"Error sending trade notification: {e}")

    async def send_sniper_notification(self, sniper_data: Dict[str, Any]):
        """Send sniper discovery notification."""
        try:
            message = f"""
🎯 **New Sniper Discovered**

👤 **Wallet:** {sniper_data.get('address', 'Unknown')[:8]}...
📈 **Success Rate:** {format_percentage(sniper_data.get('success_rate', 0))}
⚡ **Avg Speed:** {sniper_data.get('avg_speed', 0):.1f}s
💎 **Best ROI:** {sniper_data.get('best_roi', 0):.1f}x
🔄 **Copy Trading:** {'✅ Enabled' if sniper_data.get('copy_enabled') else '❌ Disabled'}

This wallet has been added to our tracking list!
            """

            await self.broadcast_message(message, "snipers")

        except Exception as e:
            self.logger.error(f"Error sending sniper notification: {e}")

    async def send_pump_launch_notification(self, token_data: Dict[str, Any]):
        """Send pump.fun launch notification."""
        try:
            message = f"""
🚀 **New Pump.fun Launch**

🪙 **Token:** {token_data.get('symbol', 'Unknown')} ({token_data.get('name', 'Unknown')})
👤 **Creator:** {token_data.get('creator', 'Unknown')[:8]}...
💰 **Market Cap:** {format_sol_amount(token_data.get('market_cap', 0))}
⏰ **Launched:** {token_data.get('launch_time', 'Unknown')}

🔗 **Mint:** `{token_data.get('mint', 'Unknown')[:16]}...`

Monitoring for snipe opportunities...
            """

            await self.broadcast_message(message, "pump_launches")

        except Exception as e:
            self.logger.error(f"Error sending pump launch notification: {e}")

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle text messages."""
        if not self.is_authorized(update.effective_user.id):
            return

        text = update.message.text.lower()

        if "status" in text:
            await self.status_command(update, context)
        elif "help" in text:
            await self.help_command(update, context)
        else:
            await update.message.reply_text(
                "🤖 Use /help to see available commands or use the menu buttons!"
            )


# Global Bloom bot instance (will be initialized with token)
bloom_bot = None


def initialize_bloom_bot(token: str, authorized_users: list = None):
    """Initialize the global bloom bot instance."""
    global bloom_bot
    if bloom_bot is None:
        bloom_bot = BloomBot(token)
        if authorized_users:
            for user_id in authorized_users:
                bloom_bot.add_authorized_user(user_id)
    return bloom_bot
