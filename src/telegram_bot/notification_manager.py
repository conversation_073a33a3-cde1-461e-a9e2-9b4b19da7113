"""
Notification Manager for Telegram integration.
Handles all types of notifications and alerts.
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from enum import Enum

from src.telegram_bot.bloom_bot import bloom_bot, initialize_bloom_bot
from src.config.settings import settings
from src.utils.logger import LoggerMixin
from src.utils.helpers import format_sol_amount, format_percentage, get_current_datetime


class NotificationType(Enum):
    """Types of notifications."""

    TRADE_EXECUTED = "trade_executed"
    SNIPER_FOUND = "sniper_found"
    PUMP_LAUNCH = "pump_launch"
    WHALE_ACTIVITY = "whale_activity"
    INSIDER_TRADE = "insider_trade"
    RISK_ALERT = "risk_alert"
    EMERGENCY_STOP = "emergency_stop"
    PERFORMANCE_UPDATE = "performance_update"
    SYSTEM_STATUS = "system_status"


class NotificationManager(LoggerMixin):
    """Manages all Telegram notifications and alerts."""

    def __init__(self):
        super().__init__()
        self.notification_queue = asyncio.Queue()
        self.is_processing = False

        # Rate limiting
        self.last_notification_times = {}
        self.notification_cooldowns = {
            NotificationType.TRADE_EXECUTED: 0,  # No cooldown for trades
            NotificationType.SNIPER_FOUND: 300,  # 5 minutes
            NotificationType.PUMP_LAUNCH: 10,  # 10 seconds
            NotificationType.WHALE_ACTIVITY: 60,  # 1 minute
            NotificationType.INSIDER_TRADE: 30,  # 30 seconds
            NotificationType.RISK_ALERT: 60,  # 1 minute
            NotificationType.EMERGENCY_STOP: 0,  # No cooldown
            NotificationType.PERFORMANCE_UPDATE: 3600,  # 1 hour
            NotificationType.SYSTEM_STATUS: 1800,  # 30 minutes
        }

        # Notification templates
        self.templates = {
            NotificationType.TRADE_EXECUTED: self._format_trade_notification,
            NotificationType.SNIPER_FOUND: self._format_sniper_notification,
            NotificationType.PUMP_LAUNCH: self._format_pump_launch_notification,
            NotificationType.WHALE_ACTIVITY: self._format_whale_notification,
            NotificationType.INSIDER_TRADE: self._format_insider_notification,
            NotificationType.RISK_ALERT: self._format_risk_alert,
            NotificationType.EMERGENCY_STOP: self._format_emergency_notification,
            NotificationType.PERFORMANCE_UPDATE: self._format_performance_notification,
            NotificationType.SYSTEM_STATUS: self._format_system_notification,
        }

    async def start_processing(self):
        """Start processing notifications."""
        if self.is_processing:
            return

        self.is_processing = True
        self.logger.info("Notification manager started")

        # Send a test notification to verify the system is working
        await self._send_startup_notification()

        while self.is_processing:
            try:
                # Get notification from queue with timeout
                notification = await asyncio.wait_for(
                    self.notification_queue.get(), timeout=1.0
                )

                await self._process_notification(notification)

            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error processing notification: {e}")

    async def stop_processing(self):
        """Stop processing notifications."""
        self.is_processing = False
        self.logger.info("Notification manager stopped")

    async def send_notification(
        self,
        notification_type: NotificationType,
        data: Dict[str, Any],
        priority: str = "normal",
    ):
        """Send a notification."""
        try:
            # Check rate limiting
            if not self._check_rate_limit(notification_type):
                self.logger.debug(f"Rate limited notification: {notification_type}")
                return

            notification = {
                "type": notification_type,
                "data": data,
                "priority": priority,
                "timestamp": get_current_datetime(),
            }

            # Add to queue
            await self.notification_queue.put(notification)

        except Exception as e:
            self.logger.error(f"Error sending notification: {e}")

    async def _process_notification(self, notification: Dict[str, Any]):
        """Process a single notification."""
        try:
            if not bloom_bot or not bloom_bot.is_running:
                return

            notification_type = notification["type"]
            data = notification["data"]

            # Format the notification message
            formatter = self.templates.get(notification_type)
            if not formatter:
                self.logger.warning(
                    f"No formatter for notification type: {notification_type}"
                )
                return

            message = formatter(data)

            # Determine subscription type
            subscription_type = self._get_subscription_type(notification_type)

            # Send the notification
            await bloom_bot.broadcast_message(message, subscription_type)

            # Update rate limiting
            self.last_notification_times[notification_type] = get_current_datetime()

        except Exception as e:
            self.logger.error(f"Error processing notification: {e}")

    async def _send_startup_notification(self):
        """Send a startup notification to test the system."""
        try:
            startup_data = {
                "status": "started",
                "timestamp": get_current_datetime().strftime("%H:%M:%S UTC"),
                "message": "🌸 @BloomSolana_bot is now online and monitoring elite Solana traders!",
            }
            await self.notify_system_status(startup_data)
        except Exception as e:
            self.logger.error(f"Error sending startup notification: {e}")

    def _check_rate_limit(self, notification_type: NotificationType) -> bool:
        """Check if notification is rate limited."""
        cooldown = self.notification_cooldowns.get(notification_type, 0)
        if cooldown == 0:
            return True

        last_time = self.last_notification_times.get(notification_type)
        if not last_time:
            return True

        time_since_last = (get_current_datetime() - last_time).total_seconds()
        return time_since_last >= cooldown

    def _get_subscription_type(self, notification_type: NotificationType) -> str:
        """Get subscription type for notification."""
        mapping = {
            NotificationType.TRADE_EXECUTED: "trades",
            NotificationType.SNIPER_FOUND: "snipers",
            NotificationType.PUMP_LAUNCH: "pump_launches",
            NotificationType.WHALE_ACTIVITY: "all",
            NotificationType.INSIDER_TRADE: "all",
            NotificationType.RISK_ALERT: "alerts",
            NotificationType.EMERGENCY_STOP: "alerts",
            NotificationType.PERFORMANCE_UPDATE: "all",
            NotificationType.SYSTEM_STATUS: "alerts",
        }
        return mapping.get(notification_type, "all")

    def _format_trade_notification(self, data: Dict[str, Any]) -> str:
        """Format trade execution notification."""
        # Check if this is an early trade notification
        if data.get("is_early_trade"):
            return self._format_early_trade_notification(data)

        emoji = "🟢" if data.get("trade_type") == "buy" else "🔴"

        return f"""
{emoji} **Copy Trade Executed**

🎯 **Source:** `{data.get('source_wallet', 'Unknown')[:8]}...`
🪙 **Token:** {data.get('token_symbol', 'Unknown')}
📊 **Type:** {data.get('trade_type', 'Unknown').upper()}
💵 **Amount:** {format_sol_amount(data.get('amount_sol', 0))}
💰 **Price:** {data.get('price', 0):.8f} SOL
⚡ **Delay:** {data.get('delay_seconds', 0):.1f}s

🔗 **Tx:** `{data.get('signature', 'Unknown')[:16]}...`
        """

    def _format_early_trade_notification(self, data: Dict[str, Any]) -> str:
        """Format early trade notification."""
        action = "🟢 BOUGHT" if data.get("is_buy") else "🔴 SOLD"

        return f"""
🌸 **@BloomSolana_bot Alpha Alert**

{action} **{data.get('token_symbol', 'Unknown')}**

👤 **Elite Trader:** `{data.get('trader', 'Unknown')[:8]}...`
💵 **Amount:** {format_sol_amount(data.get('sol_amount', 0))}
🪙 **Tokens:** {data.get('token_amount', 0):,.0f}
📊 **Market Cap:** {format_sol_amount(data.get('market_cap', 0))}
⚡ **Entry Type:** {'🚀 Launch Snipe' if data.get('is_creation_trade') else '⚡ Early Entry'}

🔗 **Transaction:** `{data.get('signature', 'Unknown')[:16]}...`
🎯 **Token Mint:** `{data.get('mint', 'Unknown')[:8]}...`

💎 **Potential sniper detected - Consider copy trading!**
        """

    def _format_sniper_notification(self, data: Dict[str, Any]) -> str:
        """Format sniper discovery notification."""
        return f"""
🎯 **New Sniper Discovered**

👤 **Wallet:** `{data.get('address', 'Unknown')[:8]}...`
📈 **Success Rate:** {format_percentage(data.get('success_rate', 0))}
⚡ **Avg Speed:** {data.get('avg_speed', 0):.1f}s
💎 **Best ROI:** {data.get('best_roi', 0):.1f}x
🏆 **Total Trades:** {data.get('total_trades', 0)}
🔄 **Copy Trading:** {'✅ Enabled' if data.get('copy_enabled') else '❌ Disabled'}

This sniper has been added to our tracking list!
        """

    def _format_pump_launch_notification(self, data: Dict[str, Any]) -> str:
        """Format pump.fun launch notification."""
        return f"""
🚀 **New Pump.fun Launch**

🪙 **Token:** {data.get('symbol', 'Unknown')} - {data.get('name', 'Unknown')}
👤 **Creator:** `{data.get('creator', 'Unknown')[:8]}...`
💰 **Market Cap:** {format_sol_amount(data.get('market_cap', 0))}
⏰ **Time:** {data.get('launch_time', 'Unknown')}

🔗 **Mint:** `{data.get('mint', 'Unknown')[:16]}...`

🎯 Monitoring for snipe opportunities...
        """

    def _format_whale_notification(self, data: Dict[str, Any]) -> str:
        """Format whale activity notification."""
        return f"""
🐋 **Whale Activity Detected**

👤 **Wallet:** `{data.get('address', 'Unknown')[:8]}...`
🪙 **Token:** {data.get('token_symbol', 'Unknown')}
📊 **Type:** {data.get('trade_type', 'Unknown').upper()}
💵 **Amount:** {format_sol_amount(data.get('amount_sol', 0))}
📈 **Market Impact:** {format_percentage(data.get('market_impact', 0))}

🔍 This whale is being tracked for copy trading.
        """

    def _format_insider_notification(self, data: Dict[str, Any]) -> str:
        """Format insider trade notification."""
        return f"""
🕵️ **Insider Trade Detected**

👤 **Wallet:** `{data.get('address', 'Unknown')[:8]}...`
🪙 **Token:** {data.get('token_symbol', 'Unknown')}
⚡ **Entry Speed:** {data.get('entry_speed', 0):.1f}s after launch
💵 **Amount:** {format_sol_amount(data.get('amount_sol', 0))}
🎯 **Confidence:** {format_percentage(data.get('confidence', 0))}

📊 Unusual timing pattern detected - potential insider information.
        """

    def _format_risk_alert(self, data: Dict[str, Any]) -> str:
        """Format risk alert notification."""
        alert_type = data.get("alert_type", "Unknown")
        severity = data.get("severity", "medium")

        emoji = "🚨" if severity == "high" else "⚠️"

        return f"""
{emoji} **Risk Alert - {alert_type.upper()}**

📊 **Current Status:**
• Daily P&L: {format_sol_amount(data.get('daily_pnl', 0))}
• Daily Loss: {format_sol_amount(data.get('daily_loss', 0))}
• Active Positions: {data.get('active_positions', 0)}
• Win Rate: {format_percentage(data.get('win_rate', 0))}

⚠️ **Alert:** {data.get('message', 'Risk threshold exceeded')}

🛡️ Risk management protocols are active.
        """

    def _format_emergency_notification(self, data: Dict[str, Any]) -> str:
        """Format emergency stop notification."""
        return f"""
🚨 **EMERGENCY STOP ACTIVATED**

⏰ **Time:** {data.get('timestamp', get_current_datetime().strftime('%H:%M:%S UTC'))}
👤 **Triggered By:** {data.get('triggered_by', 'System')}
📝 **Reason:** {data.get('reason', 'Manual emergency stop')}

🛑 **Actions Taken:**
• All trading halted immediately
• Active positions being monitored
• Risk management protocols active
• Copy trading disabled

Use /settings to review and re-enable trading when safe.
        """

    def _format_performance_notification(self, data: Dict[str, Any]) -> str:
        """Format performance update notification."""
        return f"""
📊 **Performance Update - {data.get('period', '24h')}**

💰 **Trading Results:**
• Total Trades: {data.get('total_trades', 0)}
• Successful: {data.get('successful_trades', 0)}
• Win Rate: {format_percentage(data.get('win_rate', 0))}
• Total P&L: {format_sol_amount(data.get('total_pnl', 0))}

🎯 **Top Performers:**
• Best ROI: {data.get('best_roi', 0):.1f}x
• Top Sniper: `{data.get('top_sniper', 'Unknown')[:8]}...`
• Fastest Entry: {data.get('fastest_entry', 0):.1f}s

🚀 **Pump.fun Stats:**
• New Launches: {data.get('new_launches', 0)}
• Snipes Detected: {data.get('snipes_detected', 0)}

Keep up the great work! 🎉
        """

    def _format_system_notification(self, data: Dict[str, Any]) -> str:
        """Format system status notification."""
        status = data.get("status", "unknown")
        emoji = "🟢" if status == "healthy" else "🔴" if status == "error" else "🟡"

        return f"""
{emoji} **System Status Update**

🤖 **Bot Status:** {status.title()}
⏰ **Uptime:** {data.get('uptime', 'Unknown')}
🔄 **Last Update:** {data.get('last_update', 'Unknown')}

📊 **System Health:**
• Memory Usage: {data.get('memory_usage', 0):.1f}%
• CPU Usage: {data.get('cpu_usage', 0):.1f}%
• Active Connections: {data.get('active_connections', 0)}

{data.get('message', 'System operating normally.')}
        """

    # Convenience methods for common notifications
    async def notify_trade_executed(self, trade_data: Dict[str, Any]):
        """Send trade execution notification."""
        await self.send_notification(NotificationType.TRADE_EXECUTED, trade_data)

    async def notify_sniper_found(self, sniper_data: Dict[str, Any]):
        """Send sniper discovery notification."""
        await self.send_notification(NotificationType.SNIPER_FOUND, sniper_data)

    async def notify_pump_launch(self, token_data: Dict[str, Any]):
        """Send pump.fun launch notification."""
        await self.send_notification(NotificationType.PUMP_LAUNCH, token_data)

    async def notify_early_trade(self, trade_data: Dict[str, Any]):
        """Send early trade notification."""
        await self.send_notification(NotificationType.TRADE_EXECUTED, trade_data)

    async def notify_whale_activity(self, whale_data: Dict[str, Any]):
        """Send whale activity notification."""
        await self.send_notification(NotificationType.WHALE_ACTIVITY, whale_data)

    async def notify_insider_trade(self, insider_data: Dict[str, Any]):
        """Send insider trade notification."""
        await self.send_notification(NotificationType.INSIDER_TRADE, insider_data)

    async def notify_risk_alert(self, risk_data: Dict[str, Any]):
        """Send risk alert notification."""
        await self.send_notification(
            NotificationType.RISK_ALERT, risk_data, priority="high"
        )

    async def notify_emergency_stop(self, emergency_data: Dict[str, Any]):
        """Send emergency stop notification."""
        await self.send_notification(
            NotificationType.EMERGENCY_STOP, emergency_data, priority="critical"
        )

    async def notify_performance_update(self, performance_data: Dict[str, Any]):
        """Send performance update notification."""
        await self.send_notification(
            NotificationType.PERFORMANCE_UPDATE, performance_data
        )

    async def notify_system_status(self, system_data: Dict[str, Any]):
        """Send system status notification."""
        await self.send_notification(NotificationType.SYSTEM_STATUS, system_data)

    async def notify_wallet_balance_change(self, wallet_data: Dict[str, Any]):
        """Send wallet balance change notification."""
        await self.send_notification(NotificationType.TRADE_EXECUTED, wallet_data)

    async def notify_wallet_transaction(self, transaction_data: Dict[str, Any]):
        """Send wallet transaction notification."""
        await self.send_notification(NotificationType.TRADE_EXECUTED, transaction_data)


# Global notification manager instance
notification_manager = NotificationManager()
