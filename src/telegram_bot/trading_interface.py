"""
Telegram Trading Interface for direct trading commands and controls.
"""

import asyncio
from typing import Dict, Any, List, Optional
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes, CommandHandler

from src.trading.copy_trader import copy_trader
from src.trading.risk_manager import risk_manager
from src.trading.order_executor import order_executor
from src.wallet_tracker.wallet_analyzer import wallet_analyzer
from src.wallet_tracker.pumpfun_analyzer import pumpfun_analyzer
from src.data.database import db_manager, WalletRepository
from src.config.settings import settings
from src.utils.logger import LoggerMixin
from src.utils.helpers import (
    format_sol_amount,
    format_percentage,
    is_valid_solana_address,
    get_current_datetime,
)


class TradingInterface(LoggerMixin):
    """Handles trading commands and controls through Telegram."""

    def __init__(self, bloom_bot):
        super().__init__()
        self.bloom_bot = bloom_bot
        self.wallet_repo = WalletRepository(db_manager)

        # Add trading command handlers
        self._register_handlers()

    def _register_handlers(self):
        """Register trading command handlers."""
        app = self.bloom_bot.application

        # Trading commands
        app.add_handler(CommandHandler("buy", self.buy_command))
        app.add_handler(CommandHandler("sell", self.sell_command))
        app.add_handler(CommandHandler("positions", self.positions_command))
        app.add_handler(CommandHandler("balance", self.balance_command))
        app.add_handler(CommandHandler("track", self.track_command))
        app.add_handler(CommandHandler("untrack", self.untrack_command))
        app.add_handler(CommandHandler("copy", self.copy_command))
        app.add_handler(CommandHandler("stop", self.stop_command))
        app.add_handler(CommandHandler("limits", self.limits_command))

    async def buy_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /buy command for manual token purchases."""
        if not self.bloom_bot.is_authorized(update.effective_user.id):
            return

        args = context.args
        if len(args) < 2:
            await update.message.reply_text(
                "❌ Usage: /buy <token_mint> <amount_sol>\n"
                "Example: /buy EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v 0.1"
            )
            return

        try:
            token_mint = args[0]
            amount_sol = float(args[1])

            if not is_valid_solana_address(token_mint):
                await update.message.reply_text("❌ Invalid token mint address")
                return

            if (
                amount_sol <= 0
                or amount_sol > settings.trading.copy_trading.max_position_size_sol
            ):
                await update.message.reply_text(
                    f"❌ Amount must be between 0 and {settings.trading.copy_trading.max_position_size_sol} SOL"
                )
                return

            # Show confirmation
            keyboard = [
                [
                    InlineKeyboardButton(
                        "✅ Confirm Buy",
                        callback_data=f"confirm_buy_{token_mint}_{amount_sol}",
                    ),
                    InlineKeyboardButton("❌ Cancel", callback_data="cancel_trade"),
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                f"🛒 **Confirm Purchase**\n\n"
                f"Token: `{token_mint[:16]}...`\n"
                f"Amount: {format_sol_amount(amount_sol)}\n\n"
                f"⚠️ This will execute immediately!",
                reply_markup=reply_markup,
                parse_mode="Markdown",
            )

        except ValueError:
            await update.message.reply_text(
                "❌ Invalid amount. Please enter a valid number."
            )
        except Exception as e:
            await update.message.reply_text(f"❌ Error: {e}")

    async def sell_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /sell command for manual token sales."""
        if not self.bloom_bot.is_authorized(update.effective_user.id):
            return

        args = context.args
        if len(args) < 1:
            await update.message.reply_text(
                "❌ Usage: /sell <token_mint> [percentage]\n"
                "Example: /sell EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v 50\n"
                "Leave percentage empty to sell all"
            )
            return

        try:
            token_mint = args[0]
            percentage = float(args[1]) if len(args) > 1 else 100.0

            if not is_valid_solana_address(token_mint):
                await update.message.reply_text("❌ Invalid token mint address")
                return

            if percentage <= 0 or percentage > 100:
                await update.message.reply_text(
                    "❌ Percentage must be between 1 and 100"
                )
                return

            # Show confirmation
            keyboard = [
                [
                    InlineKeyboardButton(
                        "✅ Confirm Sell",
                        callback_data=f"confirm_sell_{token_mint}_{percentage}",
                    ),
                    InlineKeyboardButton("❌ Cancel", callback_data="cancel_trade"),
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                f"💰 **Confirm Sale**\n\n"
                f"Token: `{token_mint[:16]}...`\n"
                f"Amount: {percentage}% of holdings\n\n"
                f"⚠️ This will execute immediately!",
                reply_markup=reply_markup,
                parse_mode="Markdown",
            )

        except ValueError:
            await update.message.reply_text(
                "❌ Invalid percentage. Please enter a valid number."
            )
        except Exception as e:
            await update.message.reply_text(f"❌ Error: {e}")

    async def positions_command(
        self, update: Update, context: ContextTypes.DEFAULT_TYPE
    ):
        """Handle /positions command to show current positions."""
        if not self.bloom_bot.is_authorized(update.effective_user.id):
            return

        try:
            # Get current positions from risk manager
            position_summary = await risk_manager.get_position_summary()
            positions = position_summary.get("positions", [])

            if not positions:
                await update.message.reply_text("📊 No active positions")
                return

            message = "📊 **Active Positions**\n\n"

            for i, pos in enumerate(positions[:10], 1):  # Show max 10 positions
                token_mint = pos["token_mint"]
                entry_price = pos["entry_price"]
                amount_sol = pos["amount_sol"]
                hold_time = pos["hold_time_hours"]

                # Calculate current P&L (would need current price)
                current_pnl = "N/A"  # Placeholder

                message += f"**{i}. {token_mint[:8]}...**\n"
                message += f"• Entry: {entry_price:.8f} SOL\n"
                message += f"• Amount: {format_sol_amount(amount_sol)}\n"
                message += f"• Hold Time: {hold_time:.1f}h\n"
                message += f"• P&L: {current_pnl}\n\n"

            # Add summary
            total_exposure = position_summary.get("total_exposure_sol", 0)
            message += f"💰 **Total Exposure:** {format_sol_amount(total_exposure)}\n"
            message += f"📈 **Total Positions:** {len(positions)}"

            # Add action buttons
            keyboard = [
                [
                    InlineKeyboardButton(
                        "🔄 Refresh", callback_data="refresh_positions"
                    ),
                    InlineKeyboardButton(
                        "🚨 Emergency Sell All", callback_data="emergency_sell_all"
                    ),
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                message, reply_markup=reply_markup, parse_mode="Markdown"
            )

        except Exception as e:
            await update.message.reply_text(f"❌ Error getting positions: {e}")

    async def balance_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /balance command to show wallet balance."""
        if not self.bloom_bot.is_authorized(update.effective_user.id):
            return

        try:
            # This would get actual wallet balance
            # For now, show placeholder data

            message = """
💰 **Wallet Balance**

🪙 **SOL Balance:** 12.5678 SOL
💵 **USD Value:** ~$1,567.89

📊 **Allocation:**
• Available: 8.2345 SOL (65.6%)
• In Positions: 4.3333 SOL (34.4%)

⚡ **Recent Activity:**
• Last Trade: 2 minutes ago
• Daily P&L: +0.4567 SOL
• Total Trades Today: 8
            """

            keyboard = [
                [
                    InlineKeyboardButton("🔄 Refresh", callback_data="refresh_balance"),
                    InlineKeyboardButton(
                        "📊 Detailed View", callback_data="detailed_balance"
                    ),
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                message, reply_markup=reply_markup, parse_mode="Markdown"
            )

        except Exception as e:
            await update.message.reply_text(f"❌ Error getting balance: {e}")

    async def track_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /track command to add wallet to tracking."""
        if not self.bloom_bot.is_authorized(update.effective_user.id):
            return

        args = context.args
        if not args:
            await update.message.reply_text(
                "❌ Usage: /track <wallet_address>\n"
                "Example: /track 9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM"
            )
            return

        try:
            wallet_address = args[0]

            if not is_valid_solana_address(wallet_address):
                await update.message.reply_text("❌ Invalid wallet address")
                return

            # Check if already tracked
            wallet = await self.wallet_repo.get_wallet_by_address(wallet_address)

            if wallet and wallet.is_active:
                await update.message.reply_text(
                    f"ℹ️ Wallet `{wallet_address[:8]}...` is already being tracked"
                )
                return

            # Add to tracking
            await self.wallet_repo.create_or_update_wallet(
                address=wallet_address,
                is_active=True,
                first_seen=get_current_datetime(),
            )

            # Analyze the wallet
            performance = await wallet_analyzer.analyze_wallet_performance(
                wallet_address
            )

            message = f"✅ **Wallet Added to Tracking**\n\n"
            message += f"📍 **Address:** `{wallet_address[:8]}...`\n"

            if performance:
                message += f"📊 **Performance:**\n"
                message += f"• Total Trades: {performance.total_trades}\n"
                message += f"• Win Rate: {format_percentage(performance.win_rate)}\n"
                message += f"• Total ROI: {performance.total_roi:.1f}x\n"
                message += f"• Max Position: {format_sol_amount(performance.max_position_size_sol)}\n"
            else:
                message += "📊 **Performance:** Analyzing...\n"

            message += "\n🔍 The wallet will be analyzed and classified automatically."

            await update.message.reply_text(message, parse_mode="Markdown")

        except Exception as e:
            await update.message.reply_text(f"❌ Error tracking wallet: {e}")

    async def copy_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /copy command to enable/disable copy trading for a wallet."""
        if not self.bloom_bot.is_authorized(update.effective_user.id):
            return

        args = context.args
        if len(args) < 2:
            await update.message.reply_text(
                "❌ Usage: /copy <wallet_address> <enable|disable>\n"
                "Example: /copy 9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM enable"
            )
            return

        try:
            wallet_address = args[0]
            action = args[1].lower()

            if not is_valid_solana_address(wallet_address):
                await update.message.reply_text("❌ Invalid wallet address")
                return

            if action not in ["enable", "disable"]:
                await update.message.reply_text(
                    "❌ Action must be 'enable' or 'disable'"
                )
                return

            # Update copy trading status
            enable_copy = action == "enable"

            await self.wallet_repo.create_or_update_wallet(
                address=wallet_address, is_copy_trading=enable_copy
            )

            status = "enabled" if enable_copy else "disabled"
            emoji = "✅" if enable_copy else "❌"

            await update.message.reply_text(
                f"{emoji} Copy trading {status} for wallet `{wallet_address[:8]}...`"
            )

        except Exception as e:
            await update.message.reply_text(f"❌ Error updating copy trading: {e}")

    async def untrack_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /untrack command to remove wallet from tracking."""
        if not self.bloom_bot.is_authorized(update.effective_user.id):
            return

        args = context.args
        if not args:
            await update.message.reply_text(
                "❌ Usage: /untrack <wallet_address>\n"
                "Example: /untrack 9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM"
            )
            return

        try:
            wallet_address = args[0]

            if not is_valid_solana_address(wallet_address):
                await update.message.reply_text("❌ Invalid wallet address")
                return

            # Remove from tracking
            await self.wallet_repo.create_or_update_wallet(
                address=wallet_address, is_active=False, is_copy_trading=False
            )

            await update.message.reply_text(
                f"✅ Wallet `{wallet_address[:8]}...` removed from tracking"
            )

        except Exception as e:
            await update.message.reply_text(f"❌ Error untracking wallet: {e}")

    async def stop_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /stop command to stop specific position or all trading."""
        if not self.bloom_bot.is_authorized(update.effective_user.id):
            return

        args = context.args
        if not args:
            # Show stop options
            keyboard = [
                [
                    InlineKeyboardButton(
                        "🛑 Stop All Trading", callback_data="stop_all_trading"
                    ),
                    InlineKeyboardButton(
                        "🚨 Emergency Stop", callback_data="emergency_stop"
                    ),
                ],
                [
                    InlineKeyboardButton(
                        "📊 Show Positions", callback_data="show_positions"
                    )
                ],
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                "🛑 **Stop Trading Options**\n\n" "Choose what you want to stop:",
                reply_markup=reply_markup,
                parse_mode="Markdown",
            )
            return

        try:
            token_mint = args[0]

            if not is_valid_solana_address(token_mint):
                await update.message.reply_text("❌ Invalid token mint address")
                return

            # Stop copy trading for specific token
            # This would integrate with the copy trader
            await update.message.reply_text(
                f"✅ Stopped copy trading for token `{token_mint[:8]}...`"
            )

        except Exception as e:
            await update.message.reply_text(f"❌ Error stopping trading: {e}")

    async def limits_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /limits command to show and update trading limits."""
        if not self.bloom_bot.is_authorized(update.effective_user.id):
            return

        try:
            risk_metrics = await risk_manager.get_risk_metrics()

            message = f"""
🛡️ **Trading Limits & Risk Management**

📊 **Current Status:**
• Daily P&L: {format_sol_amount(risk_metrics.daily_pnl)}
• Daily Loss: {format_sol_amount(risk_metrics.daily_loss)}
• Active Positions: {risk_metrics.active_positions}
• Win Rate (24h): {format_percentage(risk_metrics.win_rate_24h)}

⚙️ **Configured Limits:**
• Max Daily Loss: {format_sol_amount(settings.trading.risk_management.max_daily_loss_sol)}
• Max Position Size: {format_sol_amount(settings.trading.copy_trading.max_position_size_sol)}
• Max Concurrent Positions: {settings.trading.risk_management.max_concurrent_positions}
• Stop Loss: {format_percentage(settings.trading.risk_management.stop_loss_percentage)}
• Take Profit: {format_percentage(settings.trading.risk_management.take_profit_percentage)}

🚨 **Emergency Stop:** {'🔴 ACTIVE' if risk_manager.emergency_stop else '🟢 Normal'}
            """

            keyboard = [
                [
                    InlineKeyboardButton("🔄 Refresh", callback_data="refresh_limits"),
                    InlineKeyboardButton(
                        "🚨 Emergency Stop", callback_data="emergency_stop"
                    ),
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                message, reply_markup=reply_markup, parse_mode="Markdown"
            )

        except Exception as e:
            await update.message.reply_text(f"❌ Error getting limits: {e}")

    async def handle_trade_confirmation(
        self, query, action: str, token_mint: str, amount: str
    ):
        """Handle trade confirmation callbacks."""
        try:
            if action == "confirm_buy":
                amount_sol = float(amount)

                # Execute buy order
                result = await order_executor.execute_swap(
                    token_mint=token_mint,
                    trade_type="buy",
                    amount_sol=amount_sol,
                    slippage_tolerance=settings.trading.copy_trading.slippage_tolerance,
                )

                if result.success:
                    await query.edit_message_text(
                        f"✅ **Buy Order Executed**\n\n"
                        f"Token: `{token_mint[:16]}...`\n"
                        f"Amount: {format_sol_amount(amount_sol)}\n"
                        f"Signature: `{result.signature[:16]}...`\n\n"
                        f"🎉 Trade completed successfully!"
                    )
                else:
                    await query.edit_message_text(
                        f"❌ **Buy Order Failed**\n\n" f"Error: {result.error_message}"
                    )

            elif action == "confirm_sell":
                percentage = float(amount)

                # Execute sell order
                result = await order_executor.execute_swap(
                    token_mint=token_mint,
                    trade_type="sell",
                    amount_sol=None,  # Sell percentage of holdings
                    slippage_tolerance=settings.trading.copy_trading.slippage_tolerance,
                )

                if result.success:
                    await query.edit_message_text(
                        f"✅ **Sell Order Executed**\n\n"
                        f"Token: `{token_mint[:16]}...`\n"
                        f"Amount: {percentage}% of holdings\n"
                        f"Signature: `{result.signature[:16]}...`\n\n"
                        f"💰 Trade completed successfully!"
                    )
                else:
                    await query.edit_message_text(
                        f"❌ **Sell Order Failed**\n\n" f"Error: {result.error_message}"
                    )

        except Exception as e:
            await query.edit_message_text(f"❌ Error executing trade: {e}")


# This will be initialized with the bloom bot instance
trading_interface = None
