"""
Risk management system for the Solana Trading Bot.
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

from src.config.settings import settings
from src.utils.logger import LoggerMixin
from src.utils.helpers import get_current_datetime, lamports_to_sol


@dataclass
class RiskMetrics:
    """Risk metrics for monitoring."""
    daily_pnl: float
    daily_loss: float
    active_positions: int
    max_position_size: float
    total_exposure: float
    win_rate_24h: float


class RiskManager(LoggerMixin):
    """Manages trading risk and position limits."""
    
    def __init__(self):
        super().__init__()
        self.config = settings.trading.risk_management
        
        # Daily tracking
        self.daily_trades = []
        self.daily_pnl = 0.0
        self.daily_loss = 0.0
        self.last_reset = get_current_datetime().date()
        
        # Position tracking
        self.active_positions = {}
        self.position_sizes = {}
        
        # Risk limits
        self.max_daily_loss = self.config.max_daily_loss_sol
        self.max_concurrent_positions = self.config.max_concurrent_positions
        
        # Emergency stop
        self.emergency_stop = False
        
    async def can_execute_trade(self, trade_signal) -> bool:
        """Check if a trade can be executed based on risk constraints."""
        try:
            # Reset daily counters if new day
            self._reset_daily_counters_if_needed()
            
            # Check emergency stop
            if self.emergency_stop:
                self.logger.warning("Trade blocked: Emergency stop activated")
                return False
            
            # Check daily loss limit
            if self.daily_loss >= self.max_daily_loss:
                self.logger.warning(f"Trade blocked: Daily loss limit reached ({self.daily_loss} SOL)")
                return False
            
            # Check maximum concurrent positions
            if len(self.active_positions) >= self.max_concurrent_positions:
                self.logger.warning(f"Trade blocked: Max concurrent positions reached ({len(self.active_positions)})")
                return False
            
            # Check position size limits
            if hasattr(trade_signal, 'intended_amount_sol'):
                if trade_signal.intended_amount_sol > settings.trading.copy_trading.max_position_size_sol:
                    self.logger.warning(f"Trade blocked: Position size too large ({trade_signal.intended_amount_sol} SOL)")
                    return False
            
            # Check token-specific limits
            if hasattr(trade_signal, 'token_mint'):
                if not await self._check_token_risk(trade_signal.token_mint):
                    return False
            
            # Check wallet balance
            if not await self._check_sufficient_balance(trade_signal):
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking trade risk: {e}")
            return False
    
    async def record_trade_result(self, trade_result: Dict[str, Any]):
        """Record the result of a trade for risk tracking."""
        try:
            self._reset_daily_counters_if_needed()
            
            # Add to daily trades
            trade_record = {
                'timestamp': get_current_datetime(),
                'token_mint': trade_result.get('token_mint'),
                'trade_type': trade_result.get('trade_type'),
                'amount_sol': trade_result.get('amount_sol', 0),
                'pnl': trade_result.get('pnl', 0),
                'success': trade_result.get('success', False)
            }
            
            self.daily_trades.append(trade_record)
            
            # Update daily P&L
            pnl = trade_result.get('pnl', 0)
            self.daily_pnl += pnl
            
            if pnl < 0:
                self.daily_loss += abs(pnl)
            
            # Update position tracking
            token_mint = trade_result.get('token_mint')
            trade_type = trade_result.get('trade_type')
            
            if trade_type == 'buy' and token_mint:
                self.active_positions[token_mint] = {
                    'entry_time': get_current_datetime(),
                    'entry_price': trade_result.get('execution_price', 0),
                    'amount': trade_result.get('token_amount', 0),
                    'amount_sol': trade_result.get('amount_sol', 0)
                }
            elif trade_type == 'sell' and token_mint in self.active_positions:
                del self.active_positions[token_mint]
            
            # Check if emergency stop should be triggered
            await self._check_emergency_conditions()
            
            self.log_performance("daily_pnl", self.daily_pnl)
            self.log_performance("daily_loss", self.daily_loss)
            self.log_performance("active_positions", len(self.active_positions))
            
        except Exception as e:
            self.logger.error(f"Error recording trade result: {e}")
    
    async def _check_token_risk(self, token_mint: str) -> bool:
        """Check token-specific risk factors."""
        try:
            # Check if we already have a position in this token
            if token_mint in self.active_positions:
                self.logger.warning(f"Trade blocked: Already have position in {token_mint}")
                return False
            
            # Check token age (avoid very new tokens)
            # This would require token metadata
            
            # Check token liquidity
            # This would require DEX pool data
            
            # For now, allow all tokens
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking token risk for {token_mint}: {e}")
            return False
    
    async def _check_sufficient_balance(self, trade_signal) -> bool:
        """Check if we have sufficient balance for the trade."""
        try:
            # Get current SOL balance
            # This would need to be implemented with actual wallet balance check
            current_balance = 10.0  # Placeholder
            
            required_amount = getattr(trade_signal, 'intended_amount_sol', 0)
            
            # Keep 10% buffer
            available_balance = current_balance * 0.9
            
            if required_amount > available_balance:
                self.logger.warning(f"Trade blocked: Insufficient balance ({required_amount} > {available_balance})")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking balance: {e}")
            return False
    
    async def _check_emergency_conditions(self):
        """Check if emergency stop should be triggered."""
        try:
            # Trigger emergency stop if daily loss exceeds 80% of limit
            if self.daily_loss >= self.max_daily_loss * 0.8:
                self.emergency_stop = True
                self.logger.error(f"EMERGENCY STOP TRIGGERED: Daily loss {self.daily_loss} SOL")
                return
            
            # Check win rate in last 10 trades
            if len(self.daily_trades) >= 10:
                recent_trades = self.daily_trades[-10:]
                winning_trades = len([t for t in recent_trades if t['pnl'] > 0])
                win_rate = winning_trades / len(recent_trades)
                
                if win_rate < 0.2:  # Less than 20% win rate
                    self.emergency_stop = True
                    self.logger.error(f"EMERGENCY STOP TRIGGERED: Low win rate {win_rate:.2%}")
                    return
            
        except Exception as e:
            self.logger.error(f"Error checking emergency conditions: {e}")
    
    def _reset_daily_counters_if_needed(self):
        """Reset daily counters if it's a new day."""
        current_date = get_current_datetime().date()
        
        if current_date > self.last_reset:
            self.daily_trades = []
            self.daily_pnl = 0.0
            self.daily_loss = 0.0
            self.last_reset = current_date
            self.emergency_stop = False  # Reset emergency stop daily
            
            self.logger.info("Daily risk counters reset")
    
    async def get_risk_metrics(self) -> RiskMetrics:
        """Get current risk metrics."""
        try:
            self._reset_daily_counters_if_needed()
            
            # Calculate 24h win rate
            win_rate_24h = 0.0
            if self.daily_trades:
                winning_trades = len([t for t in self.daily_trades if t['pnl'] > 0])
                win_rate_24h = winning_trades / len(self.daily_trades)
            
            # Calculate total exposure
            total_exposure = sum(pos['amount_sol'] for pos in self.active_positions.values())
            
            # Calculate max position size
            max_position_size = max(
                (pos['amount_sol'] for pos in self.active_positions.values()),
                default=0.0
            )
            
            return RiskMetrics(
                daily_pnl=self.daily_pnl,
                daily_loss=self.daily_loss,
                active_positions=len(self.active_positions),
                max_position_size=max_position_size,
                total_exposure=total_exposure,
                win_rate_24h=win_rate_24h
            )
            
        except Exception as e:
            self.logger.error(f"Error getting risk metrics: {e}")
            return RiskMetrics(0, 0, 0, 0, 0, 0)
    
    async def force_close_all_positions(self):
        """Force close all active positions (emergency function)."""
        try:
            self.logger.warning("Force closing all positions")
            
            for token_mint in list(self.active_positions.keys()):
                try:
                    # This would need to integrate with order executor
                    # For now, just remove from tracking
                    del self.active_positions[token_mint]
                    
                    self.logger.info(f"Force closed position in {token_mint}")
                    
                except Exception as e:
                    self.logger.error(f"Error force closing position {token_mint}: {e}")
            
            self.emergency_stop = True
            
        except Exception as e:
            self.logger.error(f"Error force closing positions: {e}")
    
    def reset_emergency_stop(self):
        """Reset emergency stop (manual intervention)."""
        self.emergency_stop = False
        self.logger.info("Emergency stop reset manually")
    
    async def get_position_summary(self) -> Dict[str, Any]:
        """Get summary of current positions."""
        try:
            positions = []
            
            for token_mint, position in self.active_positions.items():
                # Calculate current P&L (would need current prices)
                positions.append({
                    'token_mint': token_mint,
                    'entry_time': position['entry_time'].isoformat(),
                    'entry_price': position['entry_price'],
                    'amount': position['amount'],
                    'amount_sol': position['amount_sol'],
                    'hold_time_hours': (get_current_datetime() - position['entry_time']).total_seconds() / 3600
                })
            
            return {
                'total_positions': len(positions),
                'total_exposure_sol': sum(pos['amount_sol'] for pos in self.active_positions.values()),
                'positions': positions,
                'emergency_stop': self.emergency_stop,
                'daily_pnl': self.daily_pnl,
                'daily_loss': self.daily_loss
            }
            
        except Exception as e:
            self.logger.error(f"Error getting position summary: {e}")
            return {}


# Global risk manager instance
risk_manager = RiskManager()
