"""
Helper utilities for the Solana Trading Bot.
"""

import time
import asyncio
from decimal import Decimal, ROUND_DOWN
from typing import Optional, Union, Dict, Any, List
from datetime import datetime, timezone
from solders.pubkey import Pubkey as PublicKey
import base58


def lamports_to_sol(lamports: int) -> float:
    """Convert lamports to SOL."""
    return lamports / 1_000_000_000


def sol_to_lamports(sol: float) -> int:
    """Convert SOL to lamports."""
    return int(sol * 1_000_000_000)


def format_sol_amount(amount: Union[int, float], decimals: int = 4) -> str:
    """Format SOL amount for display."""
    if isinstance(amount, int):
        amount = lamports_to_sol(amount)
    return f"{amount:.{decimals}f} SOL"


def format_percentage(value: float, decimals: int = 2) -> str:
    """Format percentage for display."""
    return f"{value:.{decimals}f}%"


def calculate_percentage_change(old_value: float, new_value: float) -> float:
    """Calculate percentage change between two values."""
    if old_value == 0:
        return 0.0
    return ((new_value - old_value) / old_value) * 100


def calculate_roi(entry_price: float, exit_price: float) -> float:
    """Calculate return on investment."""
    if entry_price == 0:
        return 0.0
    return ((exit_price - entry_price) / entry_price) * 100


def is_valid_solana_address(address: str) -> bool:
    """Check if a string is a valid Solana address."""
    try:
        PublicKey(address)
        return True
    except Exception:
        return False


def pubkey_to_string(pubkey: Union[PublicKey, str]) -> str:
    """Convert various pubkey types to string."""
    if isinstance(pubkey, str):
        return pubkey
    return str(pubkey)


def string_to_pubkey(address: str) -> PublicKey:
    """Convert string to PublicKey."""
    return PublicKey(address)


def get_current_timestamp() -> int:
    """Get current Unix timestamp."""
    return int(time.time())


def get_current_datetime() -> datetime:
    """Get current datetime in UTC."""
    return datetime.now(timezone.utc)


def timestamp_to_datetime(timestamp: int) -> datetime:
    """Convert Unix timestamp to datetime."""
    return datetime.fromtimestamp(timestamp, tz=timezone.utc)


def datetime_to_timestamp(dt: datetime) -> int:
    """Convert datetime to Unix timestamp."""
    return int(dt.timestamp())


def calculate_slippage_amount(amount: float, slippage_percentage: float) -> float:
    """Calculate the minimum amount after slippage."""
    return amount * (1 - slippage_percentage)


def round_down_to_decimals(value: float, decimals: int) -> float:
    """Round down a value to specified decimal places."""
    multiplier = 10**decimals
    return float(int(value * multiplier) / multiplier)


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """Safely divide two numbers, returning default if denominator is zero."""
    if denominator == 0:
        return default
    return numerator / denominator


def calculate_position_size(
    base_size: float,
    confidence_multiplier: float,
    max_size: float,
    available_balance: float,
) -> float:
    """Calculate position size based on confidence and constraints."""
    target_size = base_size * confidence_multiplier
    target_size = min(target_size, max_size)
    target_size = min(target_size, available_balance * 0.9)  # Leave 10% buffer
    return max(target_size, 0.0)


def format_large_number(number: Union[int, float], decimals: int = 2) -> str:
    """Format large numbers with K, M, B suffixes."""
    if number >= 1_000_000_000:
        return f"{number / 1_000_000_000:.{decimals}f}B"
    elif number >= 1_000_000:
        return f"{number / 1_000_000:.{decimals}f}M"
    elif number >= 1_000:
        return f"{number / 1_000:.{decimals}f}K"
    else:
        return f"{number:.{decimals}f}"


def extract_token_accounts_from_transaction(
    transaction_data: Dict[str, Any],
) -> List[str]:
    """Extract token account addresses from transaction data."""
    token_accounts = []

    if "meta" in transaction_data and "postTokenBalances" in transaction_data["meta"]:
        for balance in transaction_data["meta"]["postTokenBalances"]:
            if "mint" in balance:
                token_accounts.append(balance["mint"])

    return list(set(token_accounts))  # Remove duplicates


def calculate_trade_metrics(trades: List[Dict[str, Any]]) -> Dict[str, float]:
    """Calculate trading performance metrics from a list of trades."""
    if not trades:
        return {
            "total_trades": 0,
            "win_rate": 0.0,
            "total_pnl": 0.0,
            "avg_pnl": 0.0,
            "max_win": 0.0,
            "max_loss": 0.0,
            "total_roi": 0.0,
        }

    total_trades = len(trades)
    winning_trades = [t for t in trades if t.get("pnl", 0) > 0]
    total_pnl = sum(t.get("pnl", 0) for t in trades)

    return {
        "total_trades": total_trades,
        "win_rate": len(winning_trades) / total_trades * 100,
        "total_pnl": total_pnl,
        "avg_pnl": total_pnl / total_trades,
        "max_win": max((t.get("pnl", 0) for t in trades), default=0),
        "max_loss": min((t.get("pnl", 0) for t in trades), default=0),
        "total_roi": sum(t.get("roi", 0) for t in trades),
    }


async def retry_async(
    func,
    max_retries: int = 3,
    delay: float = 1.0,
    backoff_multiplier: float = 2.0,
    exceptions: tuple = (Exception,),
):
    """Retry an async function with exponential backoff."""
    for attempt in range(max_retries):
        try:
            return await func()
        except exceptions as e:
            if attempt == max_retries - 1:
                raise e
            await asyncio.sleep(delay * (backoff_multiplier**attempt))


def validate_config_values(config: Dict[str, Any]) -> List[str]:
    """Validate configuration values and return list of errors."""
    errors = []

    # Validate trading config
    trading = config.get("trading", {})
    if trading.get("risk_management", {}).get("stop_loss_percentage", 0) <= 0:
        errors.append("Stop loss percentage must be greater than 0")

    if trading.get("risk_management", {}).get("take_profit_percentage", 0) <= 0:
        errors.append("Take profit percentage must be greater than 0")

    # Validate wallet tracking config
    wallet_tracking = config.get("wallet_tracking", {})
    if wallet_tracking.get("min_roi_threshold", 0) <= 0:
        errors.append("Minimum ROI threshold must be greater than 0")

    return errors
