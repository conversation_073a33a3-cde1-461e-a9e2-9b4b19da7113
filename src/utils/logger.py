"""
Logging utilities for the Solana Trading Bot.
"""

import os
import logging
import structlog
from logging.handlers import RotatingFileHandler
from typing import Any, Dict
from rich.console import Console
from rich.logging import <PERSON><PERSON>andler

from src.config.settings import settings


def setup_logging() -> structlog.stdlib.BoundLogger:
    """Set up structured logging with both file and console output."""
    
    # Create logs directory if it doesn't exist
    log_dir = os.path.dirname(settings.logging.file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # Configure standard logging
    logging.basicConfig(
        level=getattr(logging, settings.logging.level.upper()),
        format=settings.logging.format,
        handlers=[
            # File handler with rotation
            RotatingFileHandler(
                settings.logging.file,
                maxBytes=settings.logging.max_file_size_mb * 1024 * 1024,
                backupCount=settings.logging.backup_count
            ),
            # Rich console handler for better formatting
            RichHandler(
                console=Console(stderr=True),
                show_time=True,
                show_path=True,
                markup=True
            )
        ]
    )
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    return structlog.get_logger("solana_trading_bot")


def get_logger(name: str) -> structlog.stdlib.BoundLogger:
    """Get a logger instance for a specific module."""
    return structlog.get_logger(name)


class LoggerMixin:
    """Mixin class to add logging capabilities to any class."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.logger = get_logger(self.__class__.__name__)
    
    def log_trade_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """Log trading-related events with structured data."""
        self.logger.info(
            "Trade event",
            event_type=event_type,
            **data
        )
    
    def log_wallet_event(self, wallet_address: str, event_type: str, data: Dict[str, Any]) -> None:
        """Log wallet-related events with structured data."""
        self.logger.info(
            "Wallet event",
            wallet_address=wallet_address,
            event_type=event_type,
            **data
        )
    
    def log_error(self, error: Exception, context: Dict[str, Any] = None) -> None:
        """Log errors with context information."""
        self.logger.error(
            "Error occurred",
            error=str(error),
            error_type=type(error).__name__,
            **(context or {})
        )
    
    def log_performance(self, metric_name: str, value: float, context: Dict[str, Any] = None) -> None:
        """Log performance metrics."""
        self.logger.info(
            "Performance metric",
            metric_name=metric_name,
            value=value,
            **(context or {})
        )


# Initialize the main logger
main_logger = setup_logging()
