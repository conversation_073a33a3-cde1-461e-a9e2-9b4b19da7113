#!/usr/bin/env python3
"""
Fixed Pump.fun API wrapper with proper error handling and fallbacks.
Provides working alternatives to broken endpoints.
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import logging

from src.config.settings import settings

logger = logging.getLogger(__name__)


class PumpFunAPIFixed:
    """
    Fixed Pump.fun API client with proper error handling and fallbacks.
    Avoids broken endpoints and provides working alternatives.
    """

    def __init__(self):
        self.base_url_v3 = "https://frontend-api-v3.pump.fun"
        self.session = None

        # Working endpoints (tested and verified)
        self.working_endpoints = {
            "latest_coin": f"{self.base_url_v3}/coins/latest",
            "coins_with_params": f"{self.base_url_v3}/coins",  # Requires offset/limit
            "featured_1h": f"{self.base_url_v3}/coins/featured/1h",
            "featured_24h": f"{self.base_url_v3}/coins/featured/24h",
            "sol_price": f"{self.base_url_v3}/sol-price",
        }

        # Broken endpoints to avoid
        self.broken_endpoints = {
            "live_coins": f"{self.base_url_v3}/coins/currently-live",  # 500 error
            "v2_api": "https://frontend-api-v2.pump.fun",  # 503 error
            "v1_api": "https://frontend-api.pump.fun",  # 503 error
        }

    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=10),
            headers={"User-Agent": "SolanaBot-PumpFun/1.0"},
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()

    async def get_latest_coin(self) -> Optional[Dict[str, Any]]:
        """Get the latest coin launch."""
        try:
            async with self.session.get(
                self.working_endpoints["latest_coin"]
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info("✅ Successfully fetched latest coin")
                    return data
                else:
                    logger.warning(f"Latest coin endpoint returned {response.status}")
                    return None
        except Exception as e:
            logger.error(f"Error fetching latest coin: {e}")
            return None

    async def get_recent_coins(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent coins using the working coins endpoint with pagination."""
        try:
            params = {"offset": "0", "limit": str(limit)}
            async with self.session.get(
                self.working_endpoints["coins_with_params"], params=params
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if isinstance(data, list):
                        logger.info(f"✅ Successfully fetched {len(data)} recent coins")
                        return data
                    else:
                        logger.warning("Unexpected data format from coins endpoint")
                        return []
                else:
                    logger.warning(f"Recent coins endpoint returned {response.status}")
                    return []
        except Exception as e:
            logger.error(f"Error fetching recent coins: {e}")
            return []

    async def get_live_coins_alternative(self) -> List[Dict[str, Any]]:
        """
        Alternative to the broken 'currently-live' endpoint.
        Uses featured coins and recent coins as alternatives.
        """
        logger.info("🔄 Using alternative method for live coins (broken endpoint)")

        live_coins = []

        try:
            # Try featured coins from last hour (likely to be active)
            featured_1h = await self.get_featured_coins("1h")
            if featured_1h:
                live_coins.extend(featured_1h)
                logger.info(f"📈 Found {len(featured_1h)} featured coins (1h)")

            # If we don't have enough, get recent coins
            if len(live_coins) < 5:
                recent = await self.get_recent_coins(10)
                # Filter for coins that might be "live" (recent and active)
                for coin in recent:
                    if coin not in live_coins:
                        # Consider a coin "live" if it's recent and has activity
                        created_timestamp = coin.get("created_timestamp", 0)
                        if created_timestamp:
                            try:
                                # Handle both seconds and milliseconds timestamps
                                if created_timestamp > 1e10:  # Milliseconds
                                    created_timestamp = created_timestamp / 1000
                                created_time = datetime.fromtimestamp(created_timestamp)
                                time_diff = datetime.now() - created_time
                                if (
                                    time_diff.total_seconds() < 3600
                                ):  # Less than 1 hour old
                                    live_coins.append(coin)
                            except (ValueError, OSError):
                                # Skip coins with invalid timestamps
                                continue

                logger.info(f"🆕 Added {len(recent)} recent coins to live coins")

            logger.info(
                f"✅ Alternative live coins method returned {len(live_coins)} coins"
            )
            return live_coins[:10]  # Limit to 10

        except Exception as e:
            logger.error(f"Error in live coins alternative: {e}")
            return []

    async def get_featured_coins(self, timeframe: str = "1h") -> List[Dict[str, Any]]:
        """Get featured coins for a specific timeframe."""
        try:
            if timeframe not in ["1h", "24h"]:
                timeframe = "1h"

            endpoint = self.working_endpoints[f"featured_{timeframe}"]
            async with self.session.get(endpoint) as response:
                if response.status == 200:
                    data = await response.json()
                    if isinstance(data, list):
                        logger.info(
                            f"✅ Successfully fetched {len(data)} featured coins ({timeframe})"
                        )
                        return data
                    else:
                        logger.warning(
                            f"Unexpected data format from featured coins ({timeframe})"
                        )
                        return []
                else:
                    logger.warning(
                        f"Featured coins ({timeframe}) endpoint returned {response.status}"
                    )
                    return []
        except Exception as e:
            logger.error(f"Error fetching featured coins ({timeframe}): {e}")
            return []

    async def get_sol_price(self) -> Optional[float]:
        """Get current SOL price."""
        try:
            async with self.session.get(
                self.working_endpoints["sol_price"]
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    price = data.get("solPrice")
                    if price:
                        logger.info(f"✅ SOL price: ${price}")
                        return float(price)
                    else:
                        logger.warning("SOL price not found in response")
                        return None
                else:
                    logger.warning(f"SOL price endpoint returned {response.status}")
                    return None
        except Exception as e:
            logger.error(f"Error fetching SOL price: {e}")
            return None

    async def test_all_endpoints(self) -> Dict[str, Any]:
        """Test all working endpoints and return status."""
        results = {}

        # Test working endpoints
        for name, url in self.working_endpoints.items():
            try:
                if name == "coins_with_params":
                    params = {"offset": "0", "limit": "5"}
                    async with self.session.get(url, params=params) as response:
                        status = (
                            "✅ Working"
                            if response.status == 200
                            else f"❌ Error {response.status}"
                        )
                else:
                    async with self.session.get(url) as response:
                        status = (
                            "✅ Working"
                            if response.status == 200
                            else f"❌ Error {response.status}"
                        )

                results[name] = status

            except Exception as e:
                results[name] = f"💥 Exception: {str(e)}"

        # Test broken endpoints (to confirm they're still broken)
        for name, url in self.broken_endpoints.items():
            try:
                async with self.session.get(url) as response:
                    if response.status in [500, 503]:
                        results[name] = f"❌ Confirmed broken ({response.status})"
                    else:
                        results[name] = f"🔄 Status changed ({response.status})"
            except Exception as e:
                results[name] = f"💥 Exception: {str(e)}"

        return results

    async def get_comprehensive_token_data(
        self, limit: int = 20
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get comprehensive token data from all working sources.
        This replaces the need for broken endpoints.
        """
        logger.info("🔍 Fetching comprehensive token data from all working sources...")

        data = {
            "latest_coin": None,
            "recent_coins": [],
            "featured_1h": [],
            "featured_24h": [],
            "live_alternative": [],
            "sol_price": None,
        }

        try:
            # Fetch from all working endpoints
            tasks = [
                self.get_latest_coin(),
                self.get_recent_coins(limit),
                self.get_featured_coins("1h"),
                self.get_featured_coins("24h"),
                self.get_live_coins_alternative(),
                self.get_sol_price(),
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

            data["latest_coin"] = (
                results[0] if not isinstance(results[0], Exception) else None
            )
            data["recent_coins"] = (
                results[1] if not isinstance(results[1], Exception) else []
            )
            data["featured_1h"] = (
                results[2] if not isinstance(results[2], Exception) else []
            )
            data["featured_24h"] = (
                results[3] if not isinstance(results[3], Exception) else []
            )
            data["live_alternative"] = (
                results[4] if not isinstance(results[4], Exception) else []
            )
            data["sol_price"] = (
                results[5] if not isinstance(results[5], Exception) else None
            )

            # Summary
            total_tokens = (
                len(data["recent_coins"])
                + len(data["featured_1h"])
                + len(data["featured_24h"])
                + len(data["live_alternative"])
            )

            logger.info(
                f"✅ Comprehensive data fetch complete: {total_tokens} total tokens"
            )
            return data

        except Exception as e:
            logger.error(f"Error in comprehensive token data fetch: {e}")
            return data


# Convenience functions for easy usage
async def get_working_pump_data(limit: int = 10) -> Dict[str, Any]:
    """Get pump.fun data using only working endpoints."""
    async with PumpFunAPIFixed() as api:
        return await api.get_comprehensive_token_data(limit)


async def test_pump_api_fixed() -> Dict[str, Any]:
    """Test the fixed pump.fun API."""
    async with PumpFunAPIFixed() as api:
        return await api.test_all_endpoints()
