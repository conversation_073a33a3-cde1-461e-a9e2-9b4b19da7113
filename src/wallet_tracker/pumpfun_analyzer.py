"""
Pump.fun specific wallet analysis for identifying profitable traders.
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

from src.blockchain.pumpfun_monitor import P<PERSON><PERSON><PERSON>Token, PumpFunTrade, pumpfun_monitor
from src.wallet_tracker.wallet_analyzer import WalletPerformance, wallet_analyzer
from src.wallet_tracker.trader_classifier import TraderType, trader_classifier
from src.data.database import db_manager, WalletRepository
from src.config.settings import settings
from src.utils.logger import LoggerMixin
from src.utils.helpers import get_current_datetime, calculate_roi


@dataclass
class PumpFunWalletMetrics:
    """Pump.fun specific wallet metrics."""
    address: str
    total_pump_trades: int
    snipe_success_rate: float
    avg_entry_speed_seconds: float
    best_pump_roi: float
    total_pump_pnl: float
    graduated_token_count: int
    created_token_count: int
    early_exit_rate: float
    diamond_hands_rate: float


class PumpFunAnalyzer(LoggerMixin):
    """Analyzes wallet performance specifically on pump.fun."""
    
    def __init__(self):
        super().__init__()
        self.wallet_repo = WalletRepository(db_manager)
        
        # Tracking data
        self.wallet_trades = {}  # wallet_address -> List[PumpFunTrade]
        self.wallet_tokens = {}  # wallet_address -> Set[token_mint]
        self.snipe_attempts = {}  # wallet_address -> List[snipe_data]
        
        # Performance thresholds
        self.snipe_time_threshold = 60  # 60 seconds for snipe classification
        self.early_exit_threshold = 300  # 5 minutes for early exit
        self.diamond_hands_threshold = 3600  # 1 hour for diamond hands
        
    async def initialize(self):
        """Initialize the pump.fun analyzer."""
        try:
            # Set up callbacks for pump.fun monitor
            pumpfun_monitor.add_token_callback(self._handle_new_token)
            pumpfun_monitor.add_trade_callback(self._handle_new_trade)
            
            self.logger.info("Pump.fun analyzer initialized")
            
        except Exception as e:
            self.logger.error(f"Error initializing pump.fun analyzer: {e}")
            raise
    
    async def _handle_new_token(self, token: PumpFunToken):
        """Handle new token launch detection."""
        try:
            self.logger.info(f"New pump.fun token: {token.symbol} by {token.creator[:8]}...")
            
            # Track the creator as potential dev
            await self._analyze_token_creator(token)
            
        except Exception as e:
            self.logger.error(f"Error handling new token: {e}")
    
    async def _handle_new_trade(self, trade: PumpFunTrade, token: PumpFunToken, is_early: bool):
        """Handle new trade detection."""
        try:
            # Track the trade
            if trade.trader not in self.wallet_trades:
                self.wallet_trades[trade.trader] = []
            self.wallet_trades[trade.trader].append(trade)
            
            # Track tokens per wallet
            if trade.trader not in self.wallet_tokens:
                self.wallet_tokens[trade.trader] = set()
            self.wallet_tokens[trade.trader].add(token.mint)
            
            # Analyze if this is a snipe attempt
            if is_early and trade.is_buy:
                await self._analyze_snipe_attempt(trade, token)
            
            # Update wallet metrics
            await self._update_wallet_metrics(trade.trader)
            
        except Exception as e:
            self.logger.error(f"Error handling new trade: {e}")
    
    async def _analyze_token_creator(self, token: PumpFunToken):
        """Analyze token creator for dev classification."""
        try:
            creator = token.creator
            
            # Get existing wallet or create new one
            wallet = await self.wallet_repo.get_wallet_by_address(creator)
            if not wallet:
                wallet = await self.wallet_repo.create_or_update_wallet(
                    address=creator,
                    trader_type="dev",
                    confidence_score=0.5,
                    first_seen=token.creation_time
                )
            
            # Track token creation
            # This would be stored in a separate table in a full implementation
            self.logger.info(f"Token creator tracked: {creator[:8]}... created {token.symbol}")
            
        except Exception as e:
            self.logger.error(f"Error analyzing token creator: {e}")
    
    async def _analyze_snipe_attempt(self, trade: PumpFunTrade, token: PumpFunToken):
        """Analyze a potential snipe attempt."""
        try:
            time_since_launch = (trade.timestamp - token.creation_time).total_seconds()
            
            snipe_data = {
                'token_mint': token.mint,
                'token_symbol': token.symbol,
                'entry_time': trade.timestamp,
                'entry_speed_seconds': time_since_launch,
                'entry_price': trade.price_per_token,
                'sol_amount': trade.sol_amount,
                'market_cap_at_entry': trade.market_cap_sol,
                'is_snipe': time_since_launch <= self.snipe_time_threshold
            }
            
            # Track snipe attempt
            if trade.trader not in self.snipe_attempts:
                self.snipe_attempts[trade.trader] = []
            self.snipe_attempts[trade.trader].append(snipe_data)
            
            if snipe_data['is_snipe']:
                self.logger.info(
                    f"Snipe detected: {trade.trader[:8]}... sniped {token.symbol} "
                    f"in {time_since_launch:.1f}s with {trade.sol_amount:.4f} SOL"
                )
                
                # Classify as potential sniper
                await self._classify_potential_sniper(trade.trader)
            
        except Exception as e:
            self.logger.error(f"Error analyzing snipe attempt: {e}")
    
    async def _classify_potential_sniper(self, wallet_address: str):
        """Classify wallet as potential sniper based on pump.fun activity."""
        try:
            metrics = await self.calculate_pumpfun_metrics(wallet_address)
            
            if not metrics:
                return
            
            # Sniper criteria for pump.fun
            is_sniper = (
                metrics.snipe_success_rate >= 0.6 and  # 60% snipe success rate
                metrics.avg_entry_speed_seconds <= 30 and  # Average entry within 30 seconds
                metrics.total_pump_trades >= 5 and  # At least 5 pump.fun trades
                metrics.best_pump_roi >= 10.0  # At least one 10x trade
            )
            
            if is_sniper:
                # Update wallet classification
                await self.wallet_repo.create_or_update_wallet(
                    address=wallet_address,
                    trader_type="sniper",
                    confidence_score=0.8,
                    is_copy_trading=True,  # Enable copy trading for snipers
                    last_updated=get_current_datetime()
                )
                
                self.logger.info(f"Classified {wallet_address[:8]}... as pump.fun sniper")
            
        except Exception as e:
            self.logger.error(f"Error classifying potential sniper: {e}")
    
    async def calculate_pumpfun_metrics(self, wallet_address: str) -> Optional[PumpFunWalletMetrics]:
        """Calculate pump.fun specific metrics for a wallet."""
        try:
            trades = self.wallet_trades.get(wallet_address, [])
            snipes = self.snipe_attempts.get(wallet_address, [])
            
            if not trades:
                return None
            
            # Calculate metrics
            total_trades = len(trades)
            buy_trades = [t for t in trades if t.is_buy]
            sell_trades = [t for t in trades if not t.is_buy]
            
            # Snipe success rate
            successful_snipes = 0
            total_snipes = len([s for s in snipes if s['is_snipe']])
            
            if total_snipes > 0:
                # Calculate success based on subsequent sells
                for snipe in snipes:
                    if snipe['is_snipe']:
                        # Find corresponding sell
                        sell_trade = self._find_corresponding_sell(wallet_address, snipe['token_mint'], snipe['entry_time'])
                        if sell_trade and sell_trade.price_per_token > snipe['entry_price']:
                            successful_snipes += 1
                
                snipe_success_rate = successful_snipes / total_snipes
            else:
                snipe_success_rate = 0.0
            
            # Average entry speed
            entry_speeds = [s['entry_speed_seconds'] for s in snipes if s['is_snipe']]
            avg_entry_speed = sum(entry_speeds) / len(entry_speeds) if entry_speeds else 0
            
            # Best ROI
            best_roi = 0.0
            total_pnl = 0.0
            
            for buy_trade in buy_trades:
                sell_trade = self._find_corresponding_sell(wallet_address, buy_trade.mint, buy_trade.timestamp)
                if sell_trade:
                    roi = calculate_roi(buy_trade.price_per_token, sell_trade.price_per_token)
                    pnl = (sell_trade.price_per_token - buy_trade.price_per_token) * buy_trade.token_amount
                    
                    best_roi = max(best_roi, roi)
                    total_pnl += pnl
            
            # Token counts
            unique_tokens = len(self.wallet_tokens.get(wallet_address, set()))
            graduated_tokens = 0  # Would need to track graduation status
            created_tokens = 0  # Would need to track if wallet created tokens
            
            # Exit patterns
            early_exits = 0
            diamond_hands = 0
            
            for buy_trade in buy_trades:
                sell_trade = self._find_corresponding_sell(wallet_address, buy_trade.mint, buy_trade.timestamp)
                if sell_trade:
                    hold_time = (sell_trade.timestamp - buy_trade.timestamp).total_seconds()
                    if hold_time <= self.early_exit_threshold:
                        early_exits += 1
                    elif hold_time >= self.diamond_hands_threshold:
                        diamond_hands += 1
            
            total_completed_trades = len([t for t in buy_trades if self._find_corresponding_sell(wallet_address, t.mint, t.timestamp)])
            early_exit_rate = early_exits / total_completed_trades if total_completed_trades > 0 else 0
            diamond_hands_rate = diamond_hands / total_completed_trades if total_completed_trades > 0 else 0
            
            return PumpFunWalletMetrics(
                address=wallet_address,
                total_pump_trades=total_trades,
                snipe_success_rate=snipe_success_rate,
                avg_entry_speed_seconds=avg_entry_speed,
                best_pump_roi=best_roi,
                total_pump_pnl=total_pnl,
                graduated_token_count=graduated_tokens,
                created_token_count=created_tokens,
                early_exit_rate=early_exit_rate,
                diamond_hands_rate=diamond_hands_rate
            )
            
        except Exception as e:
            self.logger.error(f"Error calculating pump.fun metrics: {e}")
            return None
    
    def _find_corresponding_sell(self, wallet_address: str, token_mint: str, after_time: datetime) -> Optional[PumpFunTrade]:
        """Find the corresponding sell trade for a buy trade."""
        trades = self.wallet_trades.get(wallet_address, [])
        
        for trade in trades:
            if (trade.mint == token_mint and 
                not trade.is_buy and 
                trade.timestamp > after_time):
                return trade
        
        return None
    
    async def _update_wallet_metrics(self, wallet_address: str):
        """Update wallet metrics based on pump.fun activity."""
        try:
            metrics = await self.calculate_pumpfun_metrics(wallet_address)
            
            if not metrics:
                return
            
            # Update wallet in database with pump.fun specific metrics
            await self.wallet_repo.create_or_update_wallet(
                address=wallet_address,
                total_trades=metrics.total_pump_trades,
                total_pnl_sol=metrics.total_pump_pnl,
                last_updated=get_current_datetime()
            )
            
        except Exception as e:
            self.logger.error(f"Error updating wallet metrics: {e}")
    
    async def get_top_pump_snipers(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get top pump.fun snipers."""
        try:
            results = []
            
            for wallet_address in self.wallet_trades.keys():
                metrics = await self.calculate_pumpfun_metrics(wallet_address)
                if metrics and metrics.snipe_success_rate >= 0.5:
                    results.append({
                        'address': wallet_address,
                        'snipe_success_rate': metrics.snipe_success_rate,
                        'avg_entry_speed': metrics.avg_entry_speed_seconds,
                        'best_roi': metrics.best_pump_roi,
                        'total_trades': metrics.total_pump_trades,
                        'total_pnl': metrics.total_pump_pnl
                    })
            
            # Sort by snipe success rate and ROI
            results.sort(key=lambda x: (x['snipe_success_rate'], x['best_roi']), reverse=True)
            
            return results[:limit]
            
        except Exception as e:
            self.logger.error(f"Error getting top pump snipers: {e}")
            return []
    
    async def get_pump_statistics(self) -> Dict[str, Any]:
        """Get overall pump.fun statistics."""
        try:
            total_wallets = len(self.wallet_trades)
            total_trades = sum(len(trades) for trades in self.wallet_trades.values())
            total_snipes = sum(len([s for s in snipes if s['is_snipe']]) for snipes in self.snipe_attempts.values())
            
            recent_launches = pumpfun_monitor.get_recent_launches(hours=24)
            
            return {
                'total_tracked_wallets': total_wallets,
                'total_pump_trades': total_trades,
                'total_snipe_attempts': total_snipes,
                'tokens_launched_24h': len(recent_launches),
                'avg_trades_per_wallet': total_trades / total_wallets if total_wallets > 0 else 0
            }
            
        except Exception as e:
            self.logger.error(f"Error getting pump statistics: {e}")
            return {}


# Global pump.fun analyzer instance
pumpfun_analyzer = PumpFunAnalyzer()
