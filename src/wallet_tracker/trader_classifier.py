"""
Trader classification system for identifying snipers, insiders, devs, and whales.
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum

from src.wallet_tracker.wallet_analyzer import WalletPerformance, wallet_analyzer
from src.blockchain.transaction_monitor import SwapTransaction
from src.data.database import db_manager, WalletRepository
from src.config.settings import settings
from src.utils.logger import LoggerMixin
from src.utils.helpers import get_current_datetime


class TraderType(Enum):
    """Enumeration of trader types."""
    SNIPER = "sniper"
    INSIDER = "insider"
    DEV = "dev"
    WHALE = "whale"
    UNKNOWN = "unknown"


class TraderClassifier(LoggerMixin):
    """Classifies traders based on their trading patterns and behavior."""
    
    def __init__(self):
        super().__init__()
        self.wallet_repo = WalletRepository(db_manager)
        self.classification_config = settings.wallet_tracking.classification
        
        # Token creation tracking for dev identification
        self.token_creators = {}
        
        # Early entry tracking for insider identification
        self.early_entries = {}
    
    async def classify_trader(self, wallet_address: str, performance: WalletPerformance) -> <PERSON>ple[TraderType, float]:
        """Classify a trader and return confidence score."""
        try:
            # Get additional data needed for classification
            trading_patterns = await self._analyze_trading_patterns(wallet_address)
            
            # Calculate scores for each trader type
            scores = {
                TraderType.SNIPER: await self._calculate_sniper_score(performance, trading_patterns),
                TraderType.INSIDER: await self._calculate_insider_score(performance, trading_patterns),
                TraderType.DEV: await self._calculate_dev_score(performance, trading_patterns),
                TraderType.WHALE: await self._calculate_whale_score(performance, trading_patterns),
            }
            
            # Find the highest scoring classification
            best_type = max(scores.keys(), key=lambda k: scores[k])
            confidence = scores[best_type]
            
            # Only classify if confidence is above threshold
            if confidence < 0.5:
                return TraderType.UNKNOWN, confidence
            
            self.logger.info(
                f"Classified trader {wallet_address} as {best_type.value} "
                f"with confidence {confidence:.2f}"
            )
            
            return best_type, confidence
            
        except Exception as e:
            self.logger.error(f"Error classifying trader {wallet_address}: {e}")
            return TraderType.UNKNOWN, 0.0
    
    async def _analyze_trading_patterns(self, wallet_address: str) -> Dict[str, Any]:
        """Analyze detailed trading patterns for a wallet."""
        try:
            # Get historical data
            historical_data = await wallet_analyzer.get_historical_wallet_data(wallet_address, days=30)
            
            patterns = {
                'avg_speed_score': 0.0,
                'early_entry_count': 0,
                'token_creation_correlation': 0.0,
                'avg_position_size': 0.0,
                'market_impact_score': 0.0,
                'unusual_timing_score': 0.0,
                'hold_time_distribution': {},
                'success_rate_by_speed': {},
                'transaction_frequency': 0.0
            }
            
            if not historical_data.get('recent_transactions'):
                return patterns
            
            transactions = historical_data['recent_transactions']
            
            # Calculate speed scores (how quickly after token creation)
            speed_scores = []
            for tx in transactions:
                if tx['type'] == 'buy':
                    speed_score = await self._calculate_transaction_speed_score(tx)
                    speed_scores.append(speed_score)
            
            patterns['avg_speed_score'] = sum(speed_scores) / len(speed_scores) if speed_scores else 0.0
            
            # Calculate early entry patterns
            patterns['early_entry_count'] = len([s for s in speed_scores if s > 0.8])
            
            # Calculate average position size
            buy_amounts = [tx['amount_sol'] for tx in transactions if tx['type'] == 'buy']
            patterns['avg_position_size'] = sum(buy_amounts) / len(buy_amounts) if buy_amounts else 0.0
            
            # Calculate transaction frequency (transactions per day)
            if transactions:
                time_span = (
                    datetime.fromisoformat(transactions[-1]['timestamp'].replace('Z', '+00:00')) -
                    datetime.fromisoformat(transactions[0]['timestamp'].replace('Z', '+00:00'))
                ).days
                patterns['transaction_frequency'] = len(transactions) / max(time_span, 1)
            
            return patterns
            
        except Exception as e:
            self.logger.error(f"Error analyzing trading patterns: {e}")
            return {}
    
    async def _calculate_transaction_speed_score(self, transaction: Dict[str, Any]) -> float:
        """Calculate how quickly a transaction occurred after token creation."""
        try:
            # This would require tracking token creation times
            # For now, return a placeholder score
            # In a real implementation, you'd compare transaction time with token creation time
            
            # Placeholder logic: assume faster transactions have higher scores
            # This would need to be implemented with actual token creation data
            return 0.5  # Placeholder
            
        except Exception as e:
            self.logger.error(f"Error calculating speed score: {e}")
            return 0.0
    
    async def _calculate_sniper_score(self, performance: WalletPerformance, patterns: Dict[str, Any]) -> float:
        """Calculate sniper classification score."""
        try:
            config = self.classification_config.sniper
            score = 0.0
            
            # Speed score (40% weight)
            if patterns.get('avg_speed_score', 0) >= config['min_speed_score']:
                score += 0.4 * (patterns['avg_speed_score'] / 1.0)
            
            # ROI performance (30% weight)
            if performance.total_roi >= config['min_roi']:
                score += 0.3 * min(performance.total_roi / (config['min_roi'] * 2), 1.0)
            
            # Hold time (20% weight) - snipers typically hold short term
            if performance.avg_hold_time_hours <= config['max_hold_time_hours']:
                score += 0.2 * (1.0 - performance.avg_hold_time_hours / config['max_hold_time_hours'])
            
            # Win rate (10% weight)
            if performance.win_rate >= 60:  # 60% minimum for snipers
                score += 0.1 * (performance.win_rate / 100)
            
            return min(score, 1.0)
            
        except Exception as e:
            self.logger.error(f"Error calculating sniper score: {e}")
            return 0.0
    
    async def _calculate_insider_score(self, performance: WalletPerformance, patterns: Dict[str, Any]) -> float:
        """Calculate insider classification score."""
        try:
            config = self.classification_config.insider
            score = 0.0
            
            # Early entry score (50% weight)
            early_entry_ratio = patterns.get('early_entry_count', 0) / max(performance.total_trades, 1)
            if early_entry_ratio >= config['min_early_entry_score']:
                score += 0.5 * (early_entry_ratio / 1.0)
            
            # Exceptional ROI (30% weight)
            if performance.total_roi >= config['min_roi']:
                score += 0.3 * min(performance.total_roi / (config['min_roi'] * 2), 1.0)
            
            # Unusual timing patterns (20% weight)
            unusual_timing = patterns.get('unusual_timing_score', 0)
            if unusual_timing >= config['unusual_timing_threshold']:
                score += 0.2 * (unusual_timing / 1.0)
            
            return min(score, 1.0)
            
        except Exception as e:
            self.logger.error(f"Error calculating insider score: {e}")
            return 0.0
    
    async def _calculate_dev_score(self, performance: WalletPerformance, patterns: Dict[str, Any]) -> float:
        """Calculate dev classification score."""
        try:
            config = self.classification_config.dev
            score = 0.0
            
            # Token creation correlation (60% weight)
            correlation = patterns.get('token_creation_correlation', 0)
            if correlation >= config['min_token_creation_correlation']:
                score += 0.6 * (correlation / 1.0)
            
            # Exceptional ROI (40% weight)
            if performance.total_roi >= config['min_roi']:
                score += 0.4 * min(performance.total_roi / (config['min_roi'] * 2), 1.0)
            
            return min(score, 1.0)
            
        except Exception as e:
            self.logger.error(f"Error calculating dev score: {e}")
            return 0.0
    
    async def _calculate_whale_score(self, performance: WalletPerformance, patterns: Dict[str, Any]) -> float:
        """Calculate whale classification score."""
        try:
            config = self.classification_config.whale
            score = 0.0
            
            # Position size (70% weight)
            if performance.max_position_size_sol >= config['min_position_size_sol']:
                score += 0.7 * min(
                    performance.max_position_size_sol / (config['min_position_size_sol'] * 5), 
                    1.0
                )
            
            # Market impact (30% weight)
            market_impact = patterns.get('market_impact_score', 0)
            if market_impact >= config['min_market_impact']:
                score += 0.3 * (market_impact / 1.0)
            
            return min(score, 1.0)
            
        except Exception as e:
            self.logger.error(f"Error calculating whale score: {e}")
            return 0.0
    
    async def update_wallet_classification(self, wallet_address: str):
        """Update wallet classification in the database."""
        try:
            # Get wallet performance
            performance = await wallet_analyzer.analyze_wallet_performance(wallet_address)
            if not performance:
                return
            
            # Classify the trader
            trader_type, confidence = await self.classify_trader(wallet_address, performance)
            
            # Update wallet in database
            await self.wallet_repo.create_or_update_wallet(
                address=wallet_address,
                trader_type=trader_type.value,
                confidence_score=confidence,
                last_updated=get_current_datetime()
            )
            
            # Enable copy trading for high-confidence profitable traders
            should_copy_trade = (
                confidence >= 0.7 and
                trader_type in [TraderType.SNIPER, TraderType.INSIDER, TraderType.DEV] and
                performance.total_roi >= settings.wallet_tracking.min_roi_threshold
            )
            
            if should_copy_trade:
                await self.wallet_repo.create_or_update_wallet(
                    address=wallet_address,
                    is_copy_trading=True
                )
                
                self.logger.info(
                    f"Enabled copy trading for {trader_type.value} wallet {wallet_address} "
                    f"with confidence {confidence:.2f}"
                )
            
        except Exception as e:
            self.logger.error(f"Error updating wallet classification: {e}")
    
    async def get_top_traders_by_type(self, trader_type: TraderType, limit: int = 20) -> List[Dict[str, Any]]:
        """Get top traders of a specific type."""
        try:
            wallets = await self.wallet_repo.get_active_wallets(trader_type.value)
            
            # Sort by confidence score and ROI
            sorted_wallets = sorted(
                wallets,
                key=lambda w: (w.confidence_score, w.total_roi),
                reverse=True
            )
            
            return [
                {
                    'address': w.address,
                    'trader_type': w.trader_type,
                    'confidence_score': w.confidence_score,
                    'total_roi': w.total_roi,
                    'win_rate': w.win_rate,
                    'total_trades': w.total_trades,
                    'is_copy_trading': w.is_copy_trading
                }
                for w in sorted_wallets[:limit]
            ]
            
        except Exception as e:
            self.logger.error(f"Error getting top traders: {e}")
            return []


# Global trader classifier instance
trader_classifier = TraderClassifier()
