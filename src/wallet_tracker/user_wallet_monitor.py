"""
User Wallet Monitor for @BloomSolana_bot
Monitors user's wallet for balance changes, transactions, and trading opportunities.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass

from solders.pubkey import Pubkey
from solana.rpc.async_api import AsyncClient
from solana.rpc.commitment import Confirmed

from ..utils.helpers import get_current_datetime, format_sol_amount
from ..data.database import DatabaseManager


@dataclass
class WalletBalance:
    """Wallet balance information."""
    sol_balance: float
    token_balances: Dict[str, float]
    total_value_sol: float
    last_updated: datetime


@dataclass
class WalletTransaction:
    """Wallet transaction information."""
    signature: str
    timestamp: datetime
    transaction_type: str  # 'send', 'receive', 'swap', 'trade'
    amount_sol: float
    token_mint: Optional[str]
    token_amount: Optional[float]
    counterparty: Optional[str]
    program_id: str


class UserWalletMonitor:
    """Monitor user's Solana wallet for activity and opportunities."""
    
    def __init__(self, config: Dict[str, Any], db_manager: DatabaseManager):
        self.config = config
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        
        # Wallet configuration
        self.wallet_address = config.get('user_wallet', {}).get('address')
        self.monitoring_enabled = config.get('user_wallet', {}).get('monitoring_enabled', True)
        self.balance_alerts = config.get('user_wallet', {}).get('balance_alerts', True)
        self.transaction_alerts = config.get('user_wallet', {}).get('transaction_alerts', True)
        self.min_alert_amount = config.get('user_wallet', {}).get('min_alert_amount_sol', 0.1)
        
        # Solana client
        self.solana_client = AsyncClient(config['solana']['rpc_url'])
        
        # State tracking
        self.current_balance: Optional[WalletBalance] = None
        self.last_transaction_signature: Optional[str] = None
        self.is_monitoring = False
        
        # Notification callback
        self.notification_callback = None
        
        if self.wallet_address:
            self.wallet_pubkey = Pubkey.from_string(self.wallet_address)
            self.logger.info(f"Initialized wallet monitor for: {self.wallet_address[:8]}...")
        else:
            self.logger.warning("No wallet address configured")
    
    def set_notification_callback(self, callback):
        """Set callback function for notifications."""
        self.notification_callback = callback
    
    async def start_monitoring(self):
        """Start monitoring the user's wallet."""
        if not self.wallet_address or not self.monitoring_enabled:
            self.logger.info("Wallet monitoring disabled or no address configured")
            return
        
        self.is_monitoring = True
        self.logger.info(f"🌸 Starting wallet monitoring for @BloomSolana_bot user: {self.wallet_address[:8]}...")
        
        # Initial balance check
        await self._update_balance()
        
        # Start monitoring tasks
        asyncio.create_task(self._monitor_balance())
        asyncio.create_task(self._monitor_transactions())
        
        # Send startup notification
        if self.notification_callback:
            await self.notification_callback({
                'type': 'wallet_monitoring_started',
                'wallet_address': self.wallet_address,
                'current_balance': self.current_balance.sol_balance if self.current_balance else 0,
                'timestamp': get_current_datetime().isoformat()
            })
    
    async def stop_monitoring(self):
        """Stop monitoring the user's wallet."""
        self.is_monitoring = False
        self.logger.info("Wallet monitoring stopped")
    
    async def _monitor_balance(self):
        """Monitor wallet balance changes."""
        while self.is_monitoring:
            try:
                previous_balance = self.current_balance.sol_balance if self.current_balance else 0
                await self._update_balance()
                
                if self.current_balance and self.balance_alerts:
                    current_balance = self.current_balance.sol_balance
                    balance_change = current_balance - previous_balance
                    
                    # Alert on significant balance changes
                    if abs(balance_change) >= self.min_alert_amount:
                        await self._send_balance_alert(balance_change, current_balance)
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error monitoring balance: {e}")
                await asyncio.sleep(60)  # Wait longer on error
    
    async def _monitor_transactions(self):
        """Monitor wallet transactions."""
        while self.is_monitoring:
            try:
                transactions = await self._get_recent_transactions()
                
                for tx in transactions:
                    if tx.signature != self.last_transaction_signature:
                        await self._process_new_transaction(tx)
                        self.last_transaction_signature = tx.signature
                
                await asyncio.sleep(15)  # Check every 15 seconds
                
            except Exception as e:
                self.logger.error(f"Error monitoring transactions: {e}")
                await asyncio.sleep(60)
    
    async def _update_balance(self):
        """Update current wallet balance."""
        try:
            # Get SOL balance
            balance_response = await self.solana_client.get_balance(self.wallet_pubkey, Confirmed)
            sol_balance = balance_response.value / 1e9  # Convert lamports to SOL
            
            # Get token balances (simplified for now)
            token_balances = {}  # TODO: Implement token balance fetching
            
            self.current_balance = WalletBalance(
                sol_balance=sol_balance,
                token_balances=token_balances,
                total_value_sol=sol_balance,  # TODO: Add token values
                last_updated=get_current_datetime()
            )
            
        except Exception as e:
            self.logger.error(f"Error updating balance: {e}")
    
    async def _get_recent_transactions(self) -> List[WalletTransaction]:
        """Get recent wallet transactions."""
        try:
            # Get recent transaction signatures
            signatures_response = await self.solana_client.get_signatures_for_address(
                self.wallet_pubkey,
                limit=10,
                commitment=Confirmed
            )
            
            transactions = []
            for sig_info in signatures_response.value:
                # Parse transaction details (simplified)
                tx = WalletTransaction(
                    signature=str(sig_info.signature),
                    timestamp=datetime.fromtimestamp(sig_info.block_time) if sig_info.block_time else get_current_datetime(),
                    transaction_type='unknown',  # TODO: Parse transaction type
                    amount_sol=0.0,  # TODO: Parse amount
                    token_mint=None,
                    token_amount=None,
                    counterparty=None,
                    program_id='unknown'
                )
                transactions.append(tx)
            
            return transactions
            
        except Exception as e:
            self.logger.error(f"Error getting transactions: {e}")
            return []
    
    async def _process_new_transaction(self, transaction: WalletTransaction):
        """Process a new transaction."""
        self.logger.info(f"💰 New transaction detected: {transaction.signature[:16]}...")
        
        if self.transaction_alerts and self.notification_callback:
            await self.notification_callback({
                'type': 'wallet_transaction',
                'signature': transaction.signature,
                'transaction_type': transaction.transaction_type,
                'amount_sol': transaction.amount_sol,
                'timestamp': transaction.timestamp.isoformat()
            })
    
    async def _send_balance_alert(self, balance_change: float, current_balance: float):
        """Send balance change alert."""
        if self.notification_callback:
            await self.notification_callback({
                'type': 'wallet_balance_change',
                'balance_change': balance_change,
                'current_balance': current_balance,
                'wallet_address': self.wallet_address,
                'timestamp': get_current_datetime().isoformat()
            })
    
    async def get_wallet_status(self) -> Dict[str, Any]:
        """Get current wallet status."""
        if not self.current_balance:
            await self._update_balance()
        
        return {
            'address': self.wallet_address,
            'sol_balance': self.current_balance.sol_balance if self.current_balance else 0,
            'token_count': len(self.current_balance.token_balances) if self.current_balance else 0,
            'total_value_sol': self.current_balance.total_value_sol if self.current_balance else 0,
            'monitoring_enabled': self.is_monitoring,
            'last_updated': self.current_balance.last_updated.isoformat() if self.current_balance else None
        }
    
    async def get_recent_activity(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent wallet activity."""
        transactions = await self._get_recent_transactions()
        
        return [{
            'signature': tx.signature,
            'type': tx.transaction_type,
            'amount_sol': tx.amount_sol,
            'timestamp': tx.timestamp.isoformat(),
            'program_id': tx.program_id
        } for tx in transactions[:limit]]
