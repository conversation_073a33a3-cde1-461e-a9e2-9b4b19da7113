"""
Wallet analysis and performance tracking for the Solana Trading Bot.
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

from src.blockchain.solana_client import solana_client
from src.blockchain.transaction_monitor import SwapTransaction
from src.data.database import db_manager, WalletRepository, TransactionRepository, TradeRepository
from src.data.models import Wallet, Transaction, Trade, Token
from src.config.settings import settings
from src.utils.logger import LoggerMixin
from src.utils.helpers import calculate_roi, calculate_trade_metrics, get_current_datetime


@dataclass
class WalletPerformance:
    """Represents wallet performance metrics."""
    address: str
    total_trades: int
    winning_trades: int
    total_pnl_sol: float
    total_roi: float
    win_rate: float
    avg_hold_time_hours: float
    max_position_size_sol: float
    sharpe_ratio: Optional[float] = None
    max_drawdown: Optional[float] = None


class WalletAnalyzer(LoggerMixin):
    """Analyzes wallet performance and trading patterns."""
    
    def __init__(self):
        super().__init__()
        self.wallet_repo = WalletRepository(db_manager)
        self.transaction_repo = TransactionRepository(db_manager)
        self.trade_repo = TradeRepository(db_manager)
        
        # Cache for wallet data
        self.wallet_cache = {}
        self.cache_expiry = {}
        self.cache_duration = timedelta(minutes=5)
    
    async def analyze_wallet_from_transaction(self, swap: SwapTransaction) -> Optional[WalletPerformance]:
        """Analyze a wallet based on a new swap transaction."""
        try:
            # Store the transaction first
            await self._store_transaction(swap)
            
            # Get or create wallet
            wallet = await self.wallet_repo.get_wallet_by_address(swap.wallet_address)
            if not wallet:
                wallet = await self.wallet_repo.create_or_update_wallet(
                    address=swap.wallet_address,
                    first_seen=swap.timestamp,
                    last_activity=swap.timestamp
                )
            
            # Update wallet activity
            await self.wallet_repo.create_or_update_wallet(
                address=swap.wallet_address,
                last_activity=swap.timestamp
            )
            
            # Analyze wallet performance
            performance = await self.analyze_wallet_performance(swap.wallet_address)
            
            # Update wallet metrics if performance meets thresholds
            if performance and self._meets_tracking_criteria(performance):
                await self._update_wallet_metrics(wallet.id, performance)
            
            return performance
            
        except Exception as e:
            self.logger.error(f"Error analyzing wallet {swap.wallet_address}: {e}")
            return None
    
    async def analyze_wallet_performance(self, wallet_address: str) -> Optional[WalletPerformance]:
        """Analyze comprehensive wallet performance."""
        try:
            # Check cache first
            if self._is_cached(wallet_address):
                return self.wallet_cache[wallet_address]
            
            # Get wallet from database
            wallet = await self.wallet_repo.get_wallet_by_address(wallet_address)
            if not wallet:
                return None
            
            # Get all transactions for the wallet
            transactions = await self.transaction_repo.get_wallet_transactions(wallet.id)
            
            if not transactions:
                return None
            
            # Group transactions into trades
            trades = await self._group_transactions_into_trades(transactions)
            
            # Calculate performance metrics
            performance = self._calculate_performance_metrics(wallet_address, trades)
            
            # Cache the result
            self._cache_performance(wallet_address, performance)
            
            return performance
            
        except Exception as e:
            self.logger.error(f"Error analyzing wallet performance for {wallet_address}: {e}")
            return None
    
    async def get_historical_wallet_data(self, wallet_address: str, days: int = 30) -> Dict[str, Any]:
        """Get historical trading data for a wallet."""
        try:
            # Get wallet
            wallet = await self.wallet_repo.get_wallet_by_address(wallet_address)
            if not wallet:
                return {}
            
            # Get recent transactions
            cutoff_date = get_current_datetime() - timedelta(days=days)
            
            # This would need to be implemented in the repository
            # For now, get all transactions and filter
            all_transactions = await self.transaction_repo.get_wallet_transactions(wallet.id)
            recent_transactions = [
                t for t in all_transactions 
                if t.block_time >= cutoff_date
            ]
            
            # Get completed trades
            trades = await self.trade_repo.get_wallet_trades(wallet.id, completed_only=True)
            recent_trades = [
                t for t in trades 
                if t.entry_time >= cutoff_date
            ]
            
            # Calculate metrics
            trade_metrics = calculate_trade_metrics([
                {
                    'pnl': t.pnl_sol,
                    'roi': t.roi_percentage
                }
                for t in recent_trades
            ])
            
            return {
                'wallet_address': wallet_address,
                'period_days': days,
                'total_transactions': len(recent_transactions),
                'total_trades': len(recent_trades),
                'metrics': trade_metrics,
                'recent_transactions': [
                    {
                        'signature': t.signature,
                        'type': t.transaction_type,
                        'amount_sol': t.amount_sol,
                        'timestamp': t.block_time.isoformat()
                    }
                    for t in recent_transactions[-10:]  # Last 10 transactions
                ]
            }
            
        except Exception as e:
            self.logger.error(f"Error getting historical data for {wallet_address}: {e}")
            return {}
    
    async def _store_transaction(self, swap: SwapTransaction):
        """Store a swap transaction in the database."""
        try:
            # Get or create token
            token = await self._get_or_create_token(swap.token_out if swap.is_buy else swap.token_in)
            
            # Get or create wallet
            wallet = await self.wallet_repo.get_wallet_by_address(swap.wallet_address)
            if not wallet:
                wallet = await self.wallet_repo.create_or_update_wallet(
                    address=swap.wallet_address
                )
            
            # Create transaction record
            await self.transaction_repo.create_transaction(
                signature=swap.signature,
                wallet_id=wallet.id,
                token_id=token.id,
                transaction_type="buy" if swap.is_buy else "sell",
                amount_sol=swap.amount_in if swap.token_in == "So11111111111111111111111111111111111111112" else swap.amount_out,
                token_amount=swap.amount_out if swap.is_buy else swap.amount_in,
                price_per_token=self._calculate_price_per_token(swap),
                dex_program=swap.dex_program,
                block_time=swap.timestamp,
                slot=swap.slot,
                raw_data=swap.raw_data
            )
            
        except Exception as e:
            self.logger.error(f"Error storing transaction: {e}")
    
    async def _get_or_create_token(self, mint_address: str) -> Token:
        """Get or create a token record."""
        # This would need to be implemented in a TokenRepository
        # For now, create a simple implementation
        async with db_manager.get_async_session() as session:
            from sqlalchemy import select
            
            result = await session.execute(
                select(Token).where(Token.mint_address == mint_address)
            )
            token = result.scalar_one_or_none()
            
            if not token:
                token = Token(
                    mint_address=mint_address,
                    first_seen=get_current_datetime()
                )
                session.add(token)
                await session.commit()
                await session.refresh(token)
            
            return token
    
    def _calculate_price_per_token(self, swap: SwapTransaction) -> float:
        """Calculate price per token from swap data."""
        if swap.is_buy:
            # Buying tokens with SOL
            if swap.amount_out > 0:
                return swap.amount_in / swap.amount_out
        else:
            # Selling tokens for SOL
            if swap.amount_in > 0:
                return swap.amount_out / swap.amount_in
        
        return 0.0
    
    async def _group_transactions_into_trades(self, transactions: List[Transaction]) -> List[Dict[str, Any]]:
        """Group buy/sell transactions into complete trades."""
        trades = []
        
        # Group by token
        token_transactions = {}
        for tx in transactions:
            if tx.token_id not in token_transactions:
                token_transactions[tx.token_id] = []
            token_transactions[tx.token_id].append(tx)
        
        # For each token, match buys with sells
        for token_id, token_txs in token_transactions.items():
            # Sort by time
            token_txs.sort(key=lambda x: x.block_time)
            
            # Simple FIFO matching
            buys = [tx for tx in token_txs if tx.transaction_type == "buy"]
            sells = [tx for tx in token_txs if tx.transaction_type == "sell"]
            
            for buy in buys:
                # Find corresponding sell
                for sell in sells:
                    if sell.block_time > buy.block_time:
                        # Calculate trade metrics
                        hold_time = (sell.block_time - buy.block_time).total_seconds() / 3600
                        roi = calculate_roi(buy.price_per_token, sell.price_per_token)
                        pnl = sell.amount_sol - buy.amount_sol
                        
                        trades.append({
                            'entry_time': buy.block_time,
                            'exit_time': sell.block_time,
                            'entry_price': buy.price_per_token,
                            'exit_price': sell.price_per_token,
                            'amount_sol': buy.amount_sol,
                            'hold_time_hours': hold_time,
                            'roi': roi,
                            'pnl': pnl,
                            'token_id': token_id
                        })
                        
                        # Remove the sell to avoid double counting
                        sells.remove(sell)
                        break
        
        return trades
    
    def _calculate_performance_metrics(self, wallet_address: str, trades: List[Dict[str, Any]]) -> WalletPerformance:
        """Calculate comprehensive performance metrics from trades."""
        if not trades:
            return WalletPerformance(
                address=wallet_address,
                total_trades=0,
                winning_trades=0,
                total_pnl_sol=0.0,
                total_roi=0.0,
                win_rate=0.0,
                avg_hold_time_hours=0.0,
                max_position_size_sol=0.0
            )
        
        # Basic metrics
        total_trades = len(trades)
        winning_trades = len([t for t in trades if t['pnl'] > 0])
        total_pnl = sum(t['pnl'] for t in trades)
        total_roi = sum(t['roi'] for t in trades)
        win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
        avg_hold_time = sum(t['hold_time_hours'] for t in trades) / total_trades
        max_position_size = max(t['amount_sol'] for t in trades)
        
        return WalletPerformance(
            address=wallet_address,
            total_trades=total_trades,
            winning_trades=winning_trades,
            total_pnl_sol=total_pnl,
            total_roi=total_roi,
            win_rate=win_rate,
            avg_hold_time_hours=avg_hold_time,
            max_position_size_sol=max_position_size
        )
    
    def _meets_tracking_criteria(self, performance: WalletPerformance) -> bool:
        """Check if wallet performance meets tracking criteria."""
        config = settings.wallet_tracking
        
        return (
            performance.total_trades >= config.min_trades and
            performance.total_roi >= config.min_roi_threshold and
            performance.win_rate >= config.min_win_rate * 100
        )
    
    async def _update_wallet_metrics(self, wallet_id: int, performance: WalletPerformance):
        """Update wallet metrics in the database."""
        try:
            await self.wallet_repo.create_or_update_wallet(
                address=performance.address,
                total_trades=performance.total_trades,
                winning_trades=performance.winning_trades,
                total_pnl_sol=performance.total_pnl_sol,
                total_roi=performance.total_roi,
                win_rate=performance.win_rate,
                avg_hold_time_hours=performance.avg_hold_time_hours,
                max_position_size_sol=performance.max_position_size_sol,
                last_updated=get_current_datetime()
            )
            
        except Exception as e:
            self.logger.error(f"Error updating wallet metrics: {e}")
    
    def _is_cached(self, wallet_address: str) -> bool:
        """Check if wallet performance is cached and not expired."""
        if wallet_address not in self.wallet_cache:
            return False
        
        if wallet_address not in self.cache_expiry:
            return False
        
        return get_current_datetime() < self.cache_expiry[wallet_address]
    
    def _cache_performance(self, wallet_address: str, performance: WalletPerformance):
        """Cache wallet performance data."""
        self.wallet_cache[wallet_address] = performance
        self.cache_expiry[wallet_address] = get_current_datetime() + self.cache_duration


# Global wallet analyzer instance
wallet_analyzer = WalletAnalyzer()
