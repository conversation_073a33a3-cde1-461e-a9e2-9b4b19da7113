#!/bin/bash

# Solana Trading Bot Production Startup Script
# This script handles the production startup with proper error handling and monitoring

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BOT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_DIR="$BOT_DIR/logs"
PID_FILE="$BOT_DIR/bot.pid"
HEALTH_CHECK_INTERVAL=60
MAX_RESTART_ATTEMPTS=5
RESTART_DELAY=30

# Functions
log_info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] [INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] [SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] [WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if .env file exists and is configured
    if [ ! -f "$BOT_DIR/.env" ]; then
        log_error ".env file not found. Please create it from .env.example"
        exit 1
    fi
    
    # Check for placeholder values in .env
    if grep -q "REPLACE_WITH" "$BOT_DIR/.env"; then
        log_error ".env file contains placeholder values. Please configure it properly."
        exit 1
    fi
    
    # Check if Python dependencies are installed
    if ! python3 -c "import solana, aiohttp, sqlalchemy" 2>/dev/null; then
        log_error "Required Python dependencies not installed. Run: pip install -r requirements.txt"
        exit 1
    fi
    
    # Create necessary directories
    mkdir -p "$LOG_DIR"
    mkdir -p "$BOT_DIR/data"
    mkdir -p "$BOT_DIR/backups"
    
    log_success "Prerequisites check passed"
}

run_health_check() {
    log_info "Running health check..."
    
    cd "$BOT_DIR"
    if python3 health_check.py; then
        log_success "Health check passed"
        return 0
    else
        log_error "Health check failed"
        return 1
    fi
}

start_bot() {
    log_info "Starting Solana Trading Bot..."
    
    cd "$BOT_DIR"
    
    # Check if bot is already running
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            log_warning "Bot is already running (PID: $pid)"
            return 0
        else
            log_info "Removing stale PID file"
            rm -f "$PID_FILE"
        fi
    fi
    
    # Start the bot in background
    nohup python3 run_bot.py > "$LOG_DIR/bot_output.log" 2>&1 &
    local bot_pid=$!
    
    # Save PID
    echo "$bot_pid" > "$PID_FILE"
    
    # Wait a moment and check if it's still running
    sleep 5
    if ps -p "$bot_pid" > /dev/null 2>&1; then
        log_success "Bot started successfully (PID: $bot_pid)"
        return 0
    else
        log_error "Bot failed to start"
        rm -f "$PID_FILE"
        return 1
    fi
}

stop_bot() {
    log_info "Stopping Solana Trading Bot..."
    
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            kill "$pid"
            
            # Wait for graceful shutdown
            local count=0
            while ps -p "$pid" > /dev/null 2>&1 && [ $count -lt 30 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            # Force kill if still running
            if ps -p "$pid" > /dev/null 2>&1; then
                log_warning "Force killing bot process"
                kill -9 "$pid"
            fi
            
            log_success "Bot stopped"
        else
            log_warning "Bot was not running"
        fi
        rm -f "$PID_FILE"
    else
        log_warning "PID file not found"
    fi
}

restart_bot() {
    log_info "Restarting bot..."
    stop_bot
    sleep "$RESTART_DELAY"
    start_bot
}

monitor_bot() {
    log_info "Starting bot monitoring..."
    
    local restart_count=0
    
    while true; do
        sleep "$HEALTH_CHECK_INTERVAL"
        
        # Check if bot process is still running
        if [ -f "$PID_FILE" ]; then
            local pid=$(cat "$PID_FILE")
            if ! ps -p "$pid" > /dev/null 2>&1; then
                log_error "Bot process died unexpectedly"
                rm -f "$PID_FILE"
                
                if [ $restart_count -lt $MAX_RESTART_ATTEMPTS ]; then
                    restart_count=$((restart_count + 1))
                    log_info "Attempting restart ($restart_count/$MAX_RESTART_ATTEMPTS)"
                    
                    if start_bot; then
                        log_success "Bot restarted successfully"
                        restart_count=0  # Reset counter on successful restart
                    else
                        log_error "Failed to restart bot"
                    fi
                else
                    log_error "Maximum restart attempts reached. Stopping monitoring."
                    exit 1
                fi
            fi
        else
            log_error "PID file missing"
            exit 1
        fi
        
        # Run health check
        if ! run_health_check; then
            log_warning "Health check failed, but bot is still running"
        fi
    done
}

show_status() {
    echo "Solana Trading Bot Status"
    echo "========================"
    
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            echo "Status: RUNNING (PID: $pid)"
            echo "Started: $(ps -o lstart= -p "$pid")"
            echo "CPU: $(ps -o %cpu= -p "$pid")%"
            echo "Memory: $(ps -o %mem= -p "$pid")%"
        else
            echo "Status: STOPPED (stale PID file)"
        fi
    else
        echo "Status: STOPPED"
    fi
    
    echo ""
    echo "Recent logs:"
    if [ -f "$LOG_DIR/trading_bot.log" ]; then
        tail -10 "$LOG_DIR/trading_bot.log"
    else
        echo "No log file found"
    fi
}

show_logs() {
    local lines=${1:-50}
    
    if [ -f "$LOG_DIR/trading_bot.log" ]; then
        tail -n "$lines" "$LOG_DIR/trading_bot.log"
    else
        log_error "Log file not found"
    fi
}

# Signal handlers
cleanup() {
    log_info "Received shutdown signal"
    stop_bot
    exit 0
}

trap cleanup SIGTERM SIGINT

# Main script logic
case "${1:-start}" in
    start)
        check_prerequisites
        if run_health_check; then
            start_bot
        else
            log_error "Health check failed. Please fix issues before starting."
            exit 1
        fi
        ;;
    stop)
        stop_bot
        ;;
    restart)
        restart_bot
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs "${2:-50}"
        ;;
    monitor)
        check_prerequisites
        if run_health_check; then
            start_bot
            monitor_bot
        else
            log_error "Health check failed. Please fix issues before starting."
            exit 1
        fi
        ;;
    health)
        run_health_check
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|logs|monitor|health}"
        echo ""
        echo "Commands:"
        echo "  start   - Start the bot"
        echo "  stop    - Stop the bot"
        echo "  restart - Restart the bot"
        echo "  status  - Show bot status"
        echo "  logs    - Show recent logs (optional: number of lines)"
        echo "  monitor - Start with monitoring (auto-restart on failure)"
        echo "  health  - Run health check"
        exit 1
        ;;
esac
