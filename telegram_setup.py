#!/usr/bin/env python3
"""
Telegram Bot Setup Script for Solana Trading Bot.
Helps configure and test the Telegram integration.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))


async def test_telegram_bot():
    """Test Telegram bot connection."""
    try:
        from src.telegram.bloom_bot import BloomBot
        from src.config.settings import settings
        
        if settings.telegram.bot_token == "YOUR_TELEGRAM_BOT_TOKEN":
            print("❌ Please set your Telegram bot token in config.yaml or .env")
            return False
        
        print("🤖 Testing Telegram bot connection...")
        
        # Create bot instance
        bot = BloomBot(settings.telegram.bot_token)
        await bot.initialize()
        
        # Test bot info
        bot_info = await bot.application.bot.get_me()
        print(f"✅ Bot connected successfully!")
        print(f"   Bot Name: {bot_info.first_name}")
        print(f"   Username: @{bot_info.username}")
        print(f"   Bot ID: {bot_info.id}")
        
        await bot.stop()
        return True
        
    except Exception as e:
        print(f"❌ Bot connection failed: {e}")
        return False


def get_user_id_instructions():
    """Show instructions for getting Telegram user ID."""
    print("\n📱 How to get your Telegram User ID:")
    print("=" * 50)
    print("1. Start a chat with @userinfobot on Telegram")
    print("2. Send any message to the bot")
    print("3. The bot will reply with your user ID")
    print("4. Add this ID to your config.yaml or .env file")
    print("\nAlternatively:")
    print("1. Start a chat with @RawDataBot")
    print("2. Send any message")
    print("3. Look for 'from' -> 'id' in the response")


def show_bot_setup_instructions():
    """Show instructions for setting up a Telegram bot."""
    print("\n🤖 How to create a Telegram Bot:")
    print("=" * 50)
    print("1. Open Telegram and search for @BotFather")
    print("2. Start a chat and send /newbot")
    print("3. Follow the instructions to create your bot")
    print("4. Copy the bot token (looks like: 123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11)")
    print("5. Add the token to your config.yaml or .env file")
    print("\nBot Commands to set with @BotFather:")
    print("/setcommands - then paste this list:")
    print("""
start - Start the bot and get welcome message
help - Show help and available commands
status - Show bot status and performance
subscribe - Subscribe to notifications
unsubscribe - Unsubscribe from notifications
wallets - Show tracked wallets
trades - Show recent trades
pump - Show pump.fun statistics
settings - Bot settings and controls
emergency - Emergency stop all trading
buy - Manual token purchase
sell - Manual token sale
positions - Show current positions
balance - Show wallet balance
track - Add wallet to tracking
copy - Enable/disable copy trading
limits - Show trading limits
    """)


async def send_test_notification():
    """Send a test notification."""
    try:
        from src.telegram.bloom_bot import BloomBot
        from src.config.settings import settings
        
        if not settings.telegram.authorized_users:
            print("❌ No authorized users configured")
            return False
        
        bot = BloomBot(settings.telegram.bot_token)
        await bot.initialize()
        
        # Add authorized users
        for user_id in settings.telegram.authorized_users:
            bot.add_authorized_user(user_id)
        
        await bot.start()
        
        # Send test message
        test_message = """
🧪 **Test Notification**

✅ Your Bloom Solana Trading Bot is working!

🎯 **Features Ready:**
• Real-time pump.fun monitoring
• Sniper detection and tracking
• Copy trading with risk management
• Telegram notifications and controls

Type /help to see all available commands.
        """
        
        await bot.broadcast_message(test_message, 'all')
        print("✅ Test notification sent!")
        
        await bot.stop()
        return True
        
    except Exception as e:
        print(f"❌ Failed to send test notification: {e}")
        return False


async def main():
    """Main setup function."""
    print("🚀 Bloom Telegram Bot Setup")
    print("=" * 40)
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "test":
            success = await test_telegram_bot()
            if success:
                print("\n✅ Telegram bot is ready!")
            else:
                print("\n❌ Please check your configuration")
        
        elif command == "notify":
            success = await send_test_notification()
            if success:
                print("\n✅ Test notification sent!")
            else:
                print("\n❌ Failed to send notification")
        
        elif command == "userid":
            get_user_id_instructions()
        
        elif command == "setup":
            show_bot_setup_instructions()
        
        else:
            print(f"❌ Unknown command: {command}")
            show_help()
    
    else:
        show_help()


def show_help():
    """Show help information."""
    print("\n📖 Telegram Bot Setup Commands:")
    print("=" * 40)
    print("python telegram_setup.py setup   - Show bot creation instructions")
    print("python telegram_setup.py userid  - Show how to get your user ID")
    print("python telegram_setup.py test    - Test bot connection")
    print("python telegram_setup.py notify  - Send test notification")
    print("\n🔧 Configuration:")
    print("1. Create a bot with @BotFather")
    print("2. Get your user ID with @userinfobot")
    print("3. Update config.yaml or .env with:")
    print("   - TELEGRAM_BOT_TOKEN=your_bot_token")
    print("   - TELEGRAM_AUTHORIZED_USERS=your_user_id")
    print("4. Run: python telegram_setup.py test")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Setup cancelled")
    except Exception as e:
        print(f"❌ Setup error: {e}")
        sys.exit(1)
