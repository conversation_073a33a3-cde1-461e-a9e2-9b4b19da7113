"""
Tests for wallet analyzer functionality.
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch

from src.wallet_tracker.wallet_analyzer import Wallet<PERSON><PERSON>y<PERSON>, WalletPerformance
from src.blockchain.transaction_monitor import SwapTransaction


@pytest.fixture
def wallet_analyzer():
    """Create a wallet analyzer instance for testing."""
    return WalletAnalyzer()


@pytest.fixture
def sample_swap_transaction():
    """Create a sample swap transaction for testing."""
    return SwapTransaction(
        signature="test_signature_123",
        wallet_address="test_wallet_address",
        token_in="So11111111111111111111111111111111111111112",  # SOL
        token_out="test_token_mint",
        amount_in=1.0,
        amount_out=1000000.0,
        dex_program="test_dex_program",
        timestamp=datetime.now(),
        slot=12345,
        is_buy=True,
        raw_data={}
    )


@pytest.mark.asyncio
async def test_analyze_wallet_from_transaction(wallet_analyzer, sample_swap_transaction):
    """Test wallet analysis from transaction."""
    with patch.object(wallet_analyzer, '_store_transaction', new_callable=AsyncMock) as mock_store:
        with patch.object(wallet_analyzer.wallet_repo, 'get_wallet_by_address', new_callable=AsyncMock) as mock_get:
            with patch.object(wallet_analyzer.wallet_repo, 'create_or_update_wallet', new_callable=AsyncMock) as mock_create:
                with patch.object(wallet_analyzer, 'analyze_wallet_performance', new_callable=AsyncMock) as mock_analyze:
                    
                    # Setup mocks
                    mock_get.return_value = None  # No existing wallet
                    mock_wallet = Mock()
                    mock_wallet.id = 1
                    mock_create.return_value = mock_wallet
                    
                    mock_performance = WalletPerformance(
                        address="test_wallet_address",
                        total_trades=5,
                        winning_trades=4,
                        total_pnl_sol=10.0,
                        total_roi=50.0,
                        win_rate=80.0,
                        avg_hold_time_hours=12.0,
                        max_position_size_sol=2.0
                    )
                    mock_analyze.return_value = mock_performance
                    
                    # Test the function
                    result = await wallet_analyzer.analyze_wallet_from_transaction(sample_swap_transaction)
                    
                    # Assertions
                    assert result is not None
                    assert result.address == "test_wallet_address"
                    assert result.total_trades == 5
                    assert result.win_rate == 80.0
                    
                    # Verify mocks were called
                    mock_store.assert_called_once()
                    mock_get.assert_called_once_with("test_wallet_address")
                    mock_create.assert_called()
                    mock_analyze.assert_called_once_with("test_wallet_address")


@pytest.mark.asyncio
async def test_calculate_performance_metrics(wallet_analyzer):
    """Test performance metrics calculation."""
    # Sample trades data
    trades = [
        {
            'entry_time': datetime.now() - timedelta(hours=24),
            'exit_time': datetime.now() - timedelta(hours=12),
            'entry_price': 1.0,
            'exit_price': 2.0,
            'amount_sol': 1.0,
            'hold_time_hours': 12.0,
            'roi': 100.0,
            'pnl': 1.0,
            'token_id': 1
        },
        {
            'entry_time': datetime.now() - timedelta(hours=48),
            'exit_time': datetime.now() - timedelta(hours=36),
            'entry_price': 1.0,
            'exit_price': 0.8,
            'amount_sol': 0.5,
            'hold_time_hours': 12.0,
            'roi': -20.0,
            'pnl': -0.1,
            'token_id': 2
        }
    ]
    
    # Calculate performance
    performance = wallet_analyzer._calculate_performance_metrics("test_wallet", trades)
    
    # Assertions
    assert performance.address == "test_wallet"
    assert performance.total_trades == 2
    assert performance.winning_trades == 1
    assert performance.win_rate == 50.0
    assert performance.total_pnl_sol == 0.9
    assert performance.total_roi == 80.0
    assert performance.avg_hold_time_hours == 12.0
    assert performance.max_position_size_sol == 1.0


def test_meets_tracking_criteria(wallet_analyzer):
    """Test tracking criteria evaluation."""
    # Performance that meets criteria
    good_performance = WalletPerformance(
        address="test_wallet",
        total_trades=10,
        winning_trades=8,
        total_pnl_sol=20.0,
        total_roi=100.0,  # 10x minimum
        win_rate=80.0,    # Above 60% minimum
        avg_hold_time_hours=12.0,
        max_position_size_sol=2.0
    )
    
    # Performance that doesn't meet criteria
    bad_performance = WalletPerformance(
        address="test_wallet",
        total_trades=2,   # Below 5 minimum
        winning_trades=1,
        total_pnl_sol=1.0,
        total_roi=5.0,    # Below 10x minimum
        win_rate=50.0,    # Below 60% minimum
        avg_hold_time_hours=12.0,
        max_position_size_sol=1.0
    )
    
    assert wallet_analyzer._meets_tracking_criteria(good_performance) == True
    assert wallet_analyzer._meets_tracking_criteria(bad_performance) == False


@pytest.mark.asyncio
async def test_group_transactions_into_trades(wallet_analyzer):
    """Test transaction grouping into trades."""
    # Mock transactions
    mock_transactions = [
        Mock(
            token_id=1,
            transaction_type="buy",
            block_time=datetime.now() - timedelta(hours=24),
            price_per_token=1.0,
            amount_sol=1.0
        ),
        Mock(
            token_id=1,
            transaction_type="sell",
            block_time=datetime.now() - timedelta(hours=12),
            price_per_token=2.0,
            amount_sol=2.0
        )
    ]
    
    # Group into trades
    trades = await wallet_analyzer._group_transactions_into_trades(mock_transactions)
    
    # Assertions
    assert len(trades) == 1
    trade = trades[0]
    assert trade['entry_price'] == 1.0
    assert trade['exit_price'] == 2.0
    assert trade['roi'] == 100.0  # 100% gain
    assert trade['pnl'] == 1.0    # 2.0 - 1.0


def test_cache_functionality(wallet_analyzer):
    """Test wallet performance caching."""
    wallet_address = "test_wallet"
    
    # Initially not cached
    assert not wallet_analyzer._is_cached(wallet_address)
    
    # Cache performance
    performance = WalletPerformance(
        address=wallet_address,
        total_trades=5,
        winning_trades=4,
        total_pnl_sol=10.0,
        total_roi=50.0,
        win_rate=80.0,
        avg_hold_time_hours=12.0,
        max_position_size_sol=2.0
    )
    
    wallet_analyzer._cache_performance(wallet_address, performance)
    
    # Now should be cached
    assert wallet_analyzer._is_cached(wallet_address)
    assert wallet_analyzer.wallet_cache[wallet_address] == performance


if __name__ == "__main__":
    pytest.main([__file__])
